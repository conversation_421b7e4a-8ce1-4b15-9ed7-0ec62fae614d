// Reusable UI Components
class Components {
    static createCard(title, content, className = '') {
        return `
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}">
                ${title ? `<h3 class="text-lg font-semibold text-gray-900 mb-4">${title}</h3>` : ''}
                ${content}
            </div>
        `;
    }

    static createButton(text, type = 'primary', size = 'md', icon = '', onClick = '') {
        const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
        
        const typeClasses = {
            primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
            secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
            success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
            danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
            outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500'
        };

        const sizeClasses = {
            sm: 'px-3 py-2 text-sm',
            md: 'px-4 py-2 text-sm',
            lg: 'px-6 py-3 text-base'
        };

        return `
            <button class="${baseClasses} ${typeClasses[type]} ${sizeClasses[size]}" ${onClick ? `onclick="${onClick}"` : ''}>
                ${icon ? `<i class="${icon} mr-2"></i>` : ''}
                ${text}
            </button>
        `;
    }

    static createModal(id, title, content, size = 'md') {
        const sizeClasses = {
            sm: 'max-w-md',
            md: 'max-w-lg',
            lg: 'max-w-2xl',
            xl: 'max-w-4xl'
        };

        return `
            <div id="${id}" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
                <div class="flex items-center justify-center min-h-screen p-4">
                    <div class="bg-white rounded-lg shadow-xl ${sizeClasses[size]} w-full">
                        <div class="flex items-center justify-between p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
                            <button onclick="Components.closeModal('${id}')" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="p-6">
                            ${content}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    static openModal(id) {
        document.getElementById(id).classList.remove('hidden');
    }

    static closeModal(id) {
        document.getElementById(id).classList.add('hidden');
    }

    static createDropdown(id, options, placeholder = 'Select option', multiple = false) {
        return `
            <div class="relative">
                <button id="${id}-trigger" class="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <span id="${id}-selected">${placeholder}</span>
                    <i class="fas fa-chevron-down float-right mt-1"></i>
                </button>
                <div id="${id}-options" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 hidden">
                    ${options.map(option => `
                        <div class="dropdown-option px-3 py-2 hover:bg-gray-100 cursor-pointer" data-value="${option.value}">
                            ${multiple ? '<input type="checkbox" class="mr-2">' : ''}
                            ${option.label}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    static createTabs(id, tabs) {
        return `
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    ${tabs.map((tab, index) => `
                        <button class="tab-button py-2 px-1 border-b-2 font-medium text-sm ${index === 0 ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}" 
                                data-tab="${tab.id}">
                            ${tab.label}
                        </button>
                    `).join('')}
                </nav>
            </div>
            <div class="mt-6">
                ${tabs.map((tab, index) => `
                    <div id="${tab.id}" class="tab-content ${index === 0 ? '' : 'hidden'}">
                        ${tab.content}
                    </div>
                `).join('')}
            </div>
        `;
    }

    static createCalendarPicker(id) {
        return `
            <div class="relative">
                <input type="text" id="${id}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500" placeholder="Select date" readonly>
                <i class="fas fa-calendar-alt absolute right-3 top-3 text-gray-400"></i>
                <div id="${id}-calendar" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 p-4 hidden">
                    <div class="text-center text-sm text-gray-600 mb-4">Calendar placeholder - Integration with date picker library needed</div>
                    <div class="grid grid-cols-7 gap-1 text-center text-xs">
                        <div class="font-semibold text-gray-600 p-2">S</div>
                        <div class="font-semibold text-gray-600 p-2">M</div>
                        <div class="font-semibold text-gray-600 p-2">T</div>
                        <div class="font-semibold text-gray-600 p-2">W</div>
                        <div class="font-semibold text-gray-600 p-2">T</div>
                        <div class="font-semibold text-gray-600 p-2">F</div>
                        <div class="font-semibold text-gray-600 p-2">S</div>
                        ${Array.from({length: 35}, (_, i) => `<div class="p-2 hover:bg-gray-100 cursor-pointer">${i % 31 + 1}</div>`).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    static createProgressBar(percentage, color = 'primary') {
        const colorClasses = {
            primary: 'bg-primary-600',
            success: 'bg-green-600',
            warning: 'bg-yellow-600',
            danger: 'bg-red-600'
        };

        return `
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="${colorClasses[color]} h-2 rounded-full transition-all duration-300" style="width: ${percentage}%"></div>
            </div>
        `;
    }

    static createBadge(text, type = 'default') {
        const typeClasses = {
            default: 'bg-gray-100 text-gray-800',
            primary: 'bg-primary-100 text-primary-800',
            success: 'bg-green-100 text-green-800',
            warning: 'bg-yellow-100 text-yellow-800',
            danger: 'bg-red-100 text-red-800',
            ai: 'bg-purple-100 text-purple-800'
        };

        return `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeClasses[type]}">${text}</span>`;
    }

    static createAvatar(src, name, size = 'md') {
        const sizeClasses = {
            sm: 'w-8 h-8 text-sm',
            md: 'w-10 h-10 text-base',
            lg: 'w-12 h-12 text-lg',
            xl: 'w-16 h-16 text-xl'
        };

        if (src) {
            return `<img src="${src}" alt="${name}" class="${sizeClasses[size]} rounded-full object-cover">`;
        } else {
            const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
            return `<div class="${sizeClasses[size]} rounded-full bg-primary-500 text-white flex items-center justify-center font-medium">${initials}</div>`;
        }
    }

    static initializeInteractivity() {
        // Initialize dropdowns
        document.addEventListener('click', (e) => {
            if (e.target.matches('[id$="-trigger"]')) {
                const id = e.target.id.replace('-trigger', '');
                const options = document.getElementById(id + '-options');
                options.classList.toggle('hidden');
            }

            // Close dropdowns when clicking outside
            if (!e.target.closest('.relative')) {
                document.querySelectorAll('[id$="-options"]').forEach(dropdown => {
                    dropdown.classList.add('hidden');
                });
            }
        });

        // Initialize tabs
        document.addEventListener('click', (e) => {
            if (e.target.matches('.tab-button')) {
                const tabId = e.target.getAttribute('data-tab');
                const tabContainer = e.target.closest('.border-b').nextElementSibling;
                
                // Update tab buttons
                e.target.parentElement.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('border-primary-500', 'text-primary-600');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });
                e.target.classList.remove('border-transparent', 'text-gray-500');
                e.target.classList.add('border-primary-500', 'text-primary-600');

                // Update tab content
                tabContainer.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tabId).classList.remove('hidden');
            }
        });

        // Initialize calendar pickers
        document.addEventListener('click', (e) => {
            if (e.target.matches('[id$="-calendar"] input') || e.target.closest('[id$="-calendar"]')) {
                const input = e.target.matches('input') ? e.target : e.target.closest('.relative').querySelector('input');
                const calendar = document.getElementById(input.id + '-calendar');
                calendar.classList.toggle('hidden');
            }
        });
    }
}

// Initialize components when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    Components.initializeInteractivity();
});
