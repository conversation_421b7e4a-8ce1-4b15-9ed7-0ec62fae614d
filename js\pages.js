// Page templates and content management
class PageManager {
    constructor() {
        this.mainContent = document.getElementById('main-content');
        this.sampleData = this.generateSampleData();
    }

    loadPage(pageId) {
        const pageContent = this.getPageContent(pageId);
        this.mainContent.innerHTML = pageContent;
        this.initializePageInteractivity(pageId);
    }

    getPageContent(pageId) {
        switch (pageId) {
            case 'login':
                return this.getLoginPage();
            case 'register':
                return this.getRegisterPage();
            case 'corporate-login':
                return this.getCorporateLoginPage();
            case 'learner-profile':
                return this.getLearnerProfilePage();
            case 'development-needs':
                return this.getDevelopmentNeedsPage();
            case 'booking':
                return this.getBookingPage();
            case 'community':
                return this.getCommunityPage();
            case 'e-modules':
                return this.getEModulesPage();
            case 'recommended-coaches':
                return this.getRecommendedCoachesPage();
            case 'coach-profile':
                return this.getCoachProfilePage();
            case 'availability':
                return this.getAvailabilityPage();
            case 'content-upload':
                return this.getContentUploadPage();
            case 'group-sessions':
                return this.getGroupSessionsPage();
            case 'payout':
                return this.getPayoutPage();
            case 'corporate-dashboard':
                return this.getCorporateDashboardPage();
            case 'employee-management':
                return this.getEmployeeManagementPage();
            case 'analytics':
                return this.getAnalyticsPage();
            case 'branded-communities':
                return this.getBrandedCommunitiesPage();
            case 'enterprise-billing':
                return this.getEnterpriseBillingPage();
            case 'integrations':
                return this.getIntegrationsPage();
            case 'ai-coach-matching':
                return this.getAICoachMatchingPage();
            case 'ai-recommendations':
                return this.getAIRecommendationsPage();
            case 'payments':
                return this.getPaymentsPage();
            case 'settings':
                return this.getSettingsPage();
            default:
                return this.getLoginPage();
        }
    }

    // Authentication Pages
    getLoginPage() {
        return `
            <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div class="max-w-md w-full space-y-8">
                    <div>
                        <div class="mx-auto h-12 w-12 bg-primary-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                            Sign in to your account
                        </h2>
                        <p class="mt-2 text-center text-sm text-gray-600">
                            Or
                            <a href="#" class="font-medium text-primary-600 hover:text-primary-500" onclick="window.navigation.navigateTo('register')">
                                create a new account
                            </a>
                        </p>
                    </div>
                    <form class="mt-8 space-y-6">
                        <div class="rounded-md shadow-sm -space-y-px">
                            <div>
                                <label for="email" class="sr-only">Email address</label>
                                <input id="email" name="email" type="email" required 
                                       class="relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
                                       placeholder="Email address">
                            </div>
                            <div>
                                <label for="password" class="sr-only">Password</label>
                                <input id="password" name="password" type="password" required 
                                       class="relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-primary-500 focus:border-primary-500 focus:z-10 sm:text-sm" 
                                       placeholder="Password">
                            </div>
                        </div>

                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input id="remember-me" name="remember-me" type="checkbox" 
                                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                                    Remember me
                                </label>
                            </div>
                            <div class="text-sm">
                                <a href="#" class="font-medium text-primary-600 hover:text-primary-500">
                                    Forgot your password?
                                </a>
                            </div>
                        </div>

                        <div>
                            ${Components.createButton('Sign in', 'primary', 'lg', '', 'handleLogin()')}
                        </div>

                        <div class="mt-6">
                            <div class="relative">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-gray-300"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                                </div>
                            </div>

                            <div class="mt-6 grid grid-cols-3 gap-3">
                                <div>
                                    ${Components.createButton('', 'outline', 'md', 'fab fa-google', 'handleGoogleLogin()')}
                                </div>
                                <div>
                                    ${Components.createButton('', 'outline', 'md', 'fab fa-linkedin', 'handleLinkedInLogin()')}
                                </div>
                                <div>
                                    ${Components.createButton('', 'outline', 'md', 'fab fa-microsoft', 'handleMicrosoftLogin()')}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    getRegisterPage() {
        return `
            <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div class="max-w-md w-full space-y-8">
                    <div>
                        <div class="mx-auto h-12 w-12 bg-primary-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-xl"></i>
                        </div>
                        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                            Create your account
                        </h2>
                        <p class="mt-2 text-center text-sm text-gray-600">
                            Or
                            <a href="#" class="font-medium text-primary-600 hover:text-primary-500" onclick="window.navigation.navigateTo('login')">
                                sign in to your existing account
                            </a>
                        </p>
                    </div>
                    <form class="mt-8 space-y-6">
                        <div class="space-y-4">
                            <div>
                                <label for="full-name" class="block text-sm font-medium text-gray-700">Full Name</label>
                                <input id="full-name" name="full-name" type="text" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                                       placeholder="John Doe">
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                                <input id="email" name="email" type="email" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                                       placeholder="<EMAIL>">
                            </div>
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                                <input id="password" name="password" type="password" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                                       placeholder="Password">
                            </div>
                            <div>
                                <label for="confirm-password" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                                <input id="confirm-password" name="confirm-password" type="password" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                                       placeholder="Confirm Password">
                            </div>
                            <div>
                                <label for="role" class="block text-sm font-medium text-gray-700">I am a</label>
                                <select id="role" name="role" 
                                        class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                                    <option value="learner">Learner/Employee</option>
                                    <option value="coach">Coach/Mentor</option>
                                    <option value="corporate">Corporate Admin</option>
                                </select>
                            </div>
                        </div>

                        <div class="flex items-center">
                            <input id="terms" name="terms" type="checkbox" required
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                            <label for="terms" class="ml-2 block text-sm text-gray-900">
                                I agree to the <a href="#" class="text-primary-600 hover:text-primary-500">Terms of Service</a> and <a href="#" class="text-primary-600 hover:text-primary-500">Privacy Policy</a>
                            </label>
                        </div>

                        <div>
                            ${Components.createButton('Create Account', 'primary', 'lg', '', 'handleRegister()')}
                        </div>

                        <div class="mt-6">
                            <div class="relative">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-gray-300"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                                </div>
                            </div>

                            <div class="mt-6 grid grid-cols-3 gap-3">
                                <div>
                                    ${Components.createButton('', 'outline', 'md', 'fab fa-google', 'handleGoogleLogin()')}
                                </div>
                                <div>
                                    ${Components.createButton('', 'outline', 'md', 'fab fa-linkedin', 'handleLinkedInLogin()')}
                                </div>
                                <div>
                                    ${Components.createButton('', 'outline', 'md', 'fab fa-microsoft', 'handleMicrosoftLogin()')}
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    getCorporateLoginPage() {
        return `
            <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div class="max-w-md w-full space-y-8">
                    <div>
                        <div class="mx-auto h-12 w-12 bg-primary-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-building text-white text-xl"></i>
                        </div>
                        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                            Corporate Admin Login
                        </h2>
                        <p class="mt-2 text-center text-sm text-gray-600">
                            Access your organization's dashboard
                        </p>
                    </div>
                    <form class="mt-8 space-y-6">
                        <div class="space-y-4">
                            <div>
                                <label for="company-domain" class="block text-sm font-medium text-gray-700">Company Domain</label>
                                <input id="company-domain" name="company-domain" type="text" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                                       placeholder="company.com">
                            </div>
                            <div>
                                <label for="admin-email" class="block text-sm font-medium text-gray-700">Admin Email</label>
                                <input id="admin-email" name="admin-email" type="email" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                                       placeholder="<EMAIL>">
                            </div>
                            <div>
                                <label for="admin-password" class="block text-sm font-medium text-gray-700">Password</label>
                                <input id="admin-password" name="admin-password" type="password" required 
                                       class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                                       placeholder="Password">
                            </div>
                        </div>

                        <div>
                            ${Components.createButton('Sign in to Corporate Dashboard', 'primary', 'lg', 'fas fa-building', 'handleCorporateLogin()')}
                        </div>

                        <div class="mt-6">
                            <div class="relative">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-gray-300"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-2 bg-gray-50 text-gray-500">Or use SSO</span>
                                </div>
                            </div>

                            <div class="mt-6 space-y-3">
                                ${Components.createButton('Continue with Azure AD', 'outline', 'lg', 'fab fa-microsoft', 'handleAzureLogin()')}
                                ${Components.createButton('Continue with Okta', 'outline', 'lg', 'fas fa-shield-alt', 'handleOktaLogin()')}
                                ${Components.createButton('Continue with SAML', 'outline', 'lg', 'fas fa-key', 'handleSAMLLogin()')}
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        `;
    }

    generateSampleData() {
        return {
            coaches: [
                { id: 1, name: 'Sarah Johnson', expertise: 'Leadership & Management', rating: 4.9, sessions: 150, languages: ['English', 'Spanish'], certifications: ['ICF PCC', 'Leadership Coach'], price: 120, image: 'images/instructor-female.png' },
                { id: 2, name: 'Michael Chen', expertise: 'Career Development', rating: 4.8, sessions: 89, languages: ['English', 'Mandarin'], certifications: ['Career Coach', 'Life Coach'], price: 100, image: 'images/instructor-male.png' },
                { id: 3, name: 'Emily Rodriguez', expertise: 'Wellness & Mindfulness', rating: 4.9, sessions: 200, languages: ['English', 'French'], certifications: ['Wellness Coach', 'Mindfulness Instructor'], price: 90, image: 'images/instructor-female.png' }
            ],
            sessions: [
                { id: 1, title: 'Leadership Fundamentals', coach: 'Sarah Johnson', date: '2024-01-15', time: '10:00 AM', type: 'Individual', status: 'Upcoming' },
                { id: 2, title: 'Career Planning Workshop', coach: 'Michael Chen', date: '2024-01-18', time: '2:00 PM', type: 'Group', status: 'Upcoming' },
                { id: 3, title: 'Mindfulness Session', coach: 'Emily Rodriguez', date: '2024-01-20', time: '9:00 AM', type: 'Individual', status: 'Completed' }
            ],
            modules: [
                { id: 1, title: 'Introduction to Leadership', duration: '45 min', progress: 100, category: 'Leadership' },
                { id: 2, title: 'Effective Communication', duration: '60 min', progress: 75, category: 'Communication' },
                { id: 3, title: 'Time Management Mastery', duration: '30 min', progress: 0, category: 'Productivity' }
            ],
            communities: [
                { id: 1, name: 'Leadership Circle', members: 45, posts: 128, category: 'Leadership' },
                { id: 2, name: 'Career Growth Hub', members: 67, posts: 89, category: 'Career' },
                { id: 3, name: 'Wellness Warriors', members: 34, posts: 156, category: 'Wellness' }
            ]
        };
    }

    // Learner/Employee Pages
    getLearnerProfilePage() {
        return `
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">My Profile</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div class="lg:col-span-1">
                            <div class="text-center">
                                <div class="mx-auto h-32 w-32 rounded-full bg-gray-300 flex items-center justify-center">
                                    <i class="fas fa-user text-4xl text-gray-500"></i>
                                </div>
                                <div class="mt-4">
                                    ${Components.createButton('Upload Photo', 'outline', 'sm', 'fas fa-camera')}
                                </div>
                            </div>
                        </div>

                        <div class="lg:col-span-2">
                            <form class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Full Name</label>
                                        <input type="text" value="John Doe" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Email</label>
                                        <input type="email" value="<EMAIL>" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Profession</label>
                                        <input type="text" value="Software Engineer" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Company</label>
                                        <input type="text" value="Tech Corp Inc." class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Country</label>
                                        <select class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                            <option>United States</option>
                                            <option>Canada</option>
                                            <option>United Kingdom</option>
                                            <option>Germany</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Language</label>
                                        <select class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                            <option>English</option>
                                            <option>Spanish</option>
                                            <option>French</option>
                                            <option>German</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Education</label>
                                    <textarea rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" placeholder="Bachelor's in Computer Science, Master's in Business Administration..."></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Professional Goals</label>
                                    <textarea rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" placeholder="Describe your career goals and aspirations..."></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Interests</label>
                                    <input type="text" placeholder="Leadership, Technology, Innovation, Team Management..." class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                </div>

                                <div class="flex justify-end space-x-3">
                                    ${Components.createButton('Cancel', 'secondary')}
                                    ${Components.createButton('Save Changes', 'primary', 'md', 'fas fa-save')}
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getDevelopmentNeedsPage() {
        const developmentAreas = [
            'Leadership & Management', 'Communication Skills', 'Technical Skills', 'Career Development',
            'Wellness & Mindfulness', 'Time Management', 'Team Building', 'Public Speaking',
            'Emotional Intelligence', 'Conflict Resolution', 'Innovation & Creativity', 'Mentoring',
            'Language Learning', 'Digital Literacy', 'Project Management', 'Sales & Marketing'
        ];

        return `
            <div class="max-w-4xl mx-auto space-y-6">
                ${Components.createCard('Select Your Development Needs', `
                    <p class="text-gray-600 mb-6">Choose the areas where you'd like to focus your professional development. You can select multiple areas.</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                        ${developmentAreas.map(area => `
                            <label class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <input type="checkbox" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                <span class="ml-3 text-sm font-medium text-gray-700">${area}</span>
                            </label>
                        `).join('')}
                    </div>

                    <div class="border-t border-gray-200 pt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Additional Notes</label>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" placeholder="Describe any specific goals or areas of focus..."></textarea>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        ${Components.createButton('Save Preferences', 'primary', 'md', 'fas fa-save')}
                        ${Components.createButton('Get AI Recommendations', 'success', 'md', 'fas fa-magic')}
                    </div>
                `)}
            </div>
        `;
    }

    getBookingPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        ${Components.createCard('Book a Session', `
                            ${Components.createTabs('booking-tabs', [
                                {
                                    id: 'individual-sessions',
                                    label: '1:1 Sessions',
                                    content: `
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Select Coach</label>
                                                ${Components.createDropdown('coach-select', [
                                                    { value: '1', label: 'Sarah Johnson - Leadership Coach' },
                                                    { value: '2', label: 'Michael Chen - Career Coach' },
                                                    { value: '3', label: 'Emily Rodriguez - Wellness Coach' }
                                                ], 'Choose a coach')}
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Date</label>
                                                ${Components.createCalendarPicker('session-date')}
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Session Type</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                    <option>Career Coaching (60 min)</option>
                                                    <option>Leadership Development (90 min)</option>
                                                    <option>Wellness Session (45 min)</option>
                                                    <option>Quick Check-in (30 min)</option>
                                                </select>
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Session Goals</label>
                                                <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" placeholder="What would you like to focus on in this session?"></textarea>
                                            </div>

                                            ${Components.createButton('Book Session', 'primary', 'md', 'fas fa-calendar-plus')}
                                        </div>
                                    `
                                },
                                {
                                    id: 'group-programs',
                                    label: 'Group Programs',
                                    content: `
                                        <div class="space-y-4">
                                            ${this.sampleData.sessions.filter(s => s.type === 'Group').map(session => `
                                                <div class="border border-gray-200 rounded-lg p-4">
                                                    <div class="flex justify-between items-start">
                                                        <div>
                                                            <h4 class="font-semibold text-gray-900">${session.title}</h4>
                                                            <p class="text-sm text-gray-600">with ${session.coach}</p>
                                                            <p class="text-sm text-gray-500">${session.date} at ${session.time}</p>
                                                        </div>
                                                        <div class="text-right">
                                                            ${Components.createBadge('5 spots left', 'warning')}
                                                            <div class="mt-2">
                                                                ${Components.createButton('Join Program', 'primary', 'sm')}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    `
                                }
                            ])}
                        `)}
                    </div>

                    <div>
                        ${Components.createCard('Upcoming Sessions', `
                            <div class="space-y-4">
                                ${this.sampleData.sessions.filter(s => s.status === 'Upcoming').map(session => `
                                    <div class="border-l-4 border-primary-500 pl-4">
                                        <h4 class="font-medium text-gray-900">${session.title}</h4>
                                        <p class="text-sm text-gray-600">${session.coach}</p>
                                        <p class="text-sm text-gray-500">${session.date} at ${session.time}</p>
                                        <div class="mt-2 space-x-2">
                                            ${Components.createButton('Join', 'primary', 'sm', 'fas fa-video')}
                                            ${Components.createButton('Reschedule', 'outline', 'sm', 'fas fa-calendar-alt')}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `)}

                        ${Components.createCard('Calendar Sync', `
                            <p class="text-sm text-gray-600 mb-4">Connect your calendar to automatically sync sessions and avoid conflicts.</p>
                            <div class="space-y-2">
                                ${Components.createButton('Connect Google Calendar', 'outline', 'sm', 'fab fa-google')}
                                ${Components.createButton('Connect Outlook', 'outline', 'sm', 'fab fa-microsoft')}
                                ${Components.createButton('Connect Apple Calendar', 'outline', 'sm', 'fab fa-apple')}
                            </div>
                        `)}
                    </div>
                </div>
            </div>
        `;
    }

    getCommunityPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div class="lg:col-span-3">
                        ${Components.createCard('Growth Circles', `
                            <div class="space-y-4">
                                ${this.sampleData.communities.map(community => `
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h3 class="font-semibold text-gray-900">${community.name}</h3>
                                                <p class="text-sm text-gray-600">${community.members} members • ${community.posts} posts</p>
                                                <div class="mt-2">
                                                    ${Components.createBadge(community.category, 'primary')}
                                                </div>
                                            </div>
                                            <div class="space-x-2">
                                                ${Components.createButton('Join', 'primary', 'sm')}
                                                ${Components.createButton('View', 'outline', 'sm')}
                                            </div>
                                        </div>

                                        <div class="mt-4 pt-4 border-t border-gray-100">
                                            <h4 class="text-sm font-medium text-gray-900 mb-2">Recent Discussions</h4>
                                            <div class="space-y-2">
                                                <div class="text-sm">
                                                    <span class="font-medium">Sarah M.</span> shared insights on effective team communication
                                                    <span class="text-gray-500">• 2h ago</span>
                                                </div>
                                                <div class="text-sm">
                                                    <span class="font-medium">Mike R.</span> asked about leadership challenges in remote teams
                                                    <span class="text-gray-500">• 5h ago</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `)}

                        ${Components.createCard('Create New Discussion', `
                            <form class="space-y-4">
                                <div>
                                    <input type="text" placeholder="Discussion title..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                </div>
                                <div>
                                    <textarea rows="3" placeholder="Share your thoughts, ask questions, or start a discussion..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div>
                                        ${Components.createDropdown('discussion-category', [
                                            { value: 'leadership', label: 'Leadership' },
                                            { value: 'career', label: 'Career' },
                                            { value: 'wellness', label: 'Wellness' },
                                            { value: 'general', label: 'General' }
                                        ], 'Select category')}
                                    </div>
                                    ${Components.createButton('Post Discussion', 'primary', 'md', 'fas fa-paper-plane')}
                                </div>
                            </form>
                        `)}
                    </div>

                    <div>
                        ${Components.createCard('Community Stats', `
                            <div class="space-y-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary-600">146</div>
                                    <div class="text-sm text-gray-600">Total Members</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">23</div>
                                    <div class="text-sm text-gray-600">Active Today</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">89</div>
                                    <div class="text-sm text-gray-600">Discussions This Week</div>
                                </div>
                            </div>
                        `)}

                        ${Components.createCard('Find Members', `
                            <div class="space-y-4">
                                <input type="text" placeholder="Search members..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">

                                <div class="space-y-3">
                                    ${['Alex Thompson', 'Maria Garcia', 'David Kim'].map(name => `
                                        <div class="flex items-center space-x-3">
                                            ${Components.createAvatar('', name, 'sm')}
                                            <div class="flex-1">
                                                <div class="text-sm font-medium text-gray-900">${name}</div>
                                                <div class="text-xs text-gray-500">Leadership Coach</div>
                                            </div>
                                            ${Components.createButton('Connect', 'outline', 'sm')}
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `)}
                    </div>
                </div>
            </div>
        `;
    }

    getEModulesPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div class="lg:col-span-3">
                        ${Components.createCard('My Learning Path', `
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-700">Overall Progress</span>
                                    <span class="text-sm text-gray-500">58% Complete</span>
                                </div>
                                ${Components.createProgressBar(58)}
                            </div>

                            <div class="space-y-4">
                                ${this.sampleData.modules.map(module => `
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-play-circle text-2xl text-primary-500"></i>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">${module.title}</h3>
                                                    <p class="text-sm text-gray-600">${module.duration} • ${module.category}</p>
                                                    <div class="mt-2">
                                                        ${Components.createProgressBar(module.progress, module.progress === 100 ? 'success' : 'primary')}
                                                        <span class="text-xs text-gray-500 mt-1">${module.progress}% complete</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="space-x-2">
                                                ${module.progress === 100
                                                    ? Components.createButton('Completed', 'success', 'sm', 'fas fa-check')
                                                    : module.progress > 0
                                                        ? Components.createButton('Continue', 'primary', 'sm', 'fas fa-play')
                                                        : Components.createButton('Start', 'primary', 'sm', 'fas fa-play')
                                                }
                                                ${Components.createButton('', 'outline', 'sm', 'fas fa-bookmark')}
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `)}

                        ${Components.createCard('Recommended Modules', `
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                ${['Advanced Leadership Strategies', 'Emotional Intelligence Mastery', 'Digital Communication Skills', 'Innovation Mindset'].map(title => `
                                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                        <div class="w-full h-32 bg-gradient-to-r from-primary-400 to-primary-600 rounded-lg mb-3 flex items-center justify-center">
                                            <i class="fas fa-play-circle text-3xl text-white"></i>
                                        </div>
                                        <h4 class="font-semibold text-gray-900 mb-2">${title}</h4>
                                        <p class="text-sm text-gray-600 mb-3">45 min • Leadership</p>
                                        <div class="flex justify-between items-center">
                                            ${Components.createBadge('AI Recommended', 'ai')}
                                            ${Components.createButton('Add to Path', 'outline', 'sm')}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `)}
                    </div>

                    <div>
                        ${Components.createCard('Learning Stats', `
                            <div class="space-y-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary-600">12</div>
                                    <div class="text-sm text-gray-600">Modules Completed</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">8.5</div>
                                    <div class="text-sm text-gray-600">Hours This Month</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">5</div>
                                    <div class="text-sm text-gray-600">Certificates Earned</div>
                                </div>
                            </div>
                        `)}

                        ${Components.createCard('Categories', `
                            <div class="space-y-2">
                                ${['Leadership', 'Communication', 'Technical Skills', 'Wellness', 'Career Development'].map(category => `
                                    <div class="flex justify-between items-center py-2">
                                        <span class="text-sm text-gray-700">${category}</span>
                                        <span class="text-xs text-gray-500">12 modules</span>
                                    </div>
                                `).join('')}
                            </div>
                        `)}
                    </div>
                </div>
            </div>
        `;
    }

    getRecommendedCoachesPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-900">Recommended Coaches</h2>
                    <div class="flex space-x-3">
                        ${Components.createButton('Filter', 'outline', 'md', 'fas fa-filter')}
                        ${Components.createButton('Get AI Recommendations', 'primary', 'md', 'fas fa-magic')}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    ${this.sampleData.coaches.map(coach => `
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                            <div class="text-center mb-4">
                                ${Components.createAvatar(coach.image, coach.name, 'xl')}
                                <h3 class="mt-3 font-semibold text-gray-900">${coach.name}</h3>
                                <p class="text-sm text-gray-600">${coach.expertise}</p>
                                <div class="mt-2 flex items-center justify-center space-x-1">
                                    <div class="flex text-yellow-400">
                                        ${Array(5).fill().map((_, i) => `<i class="fas fa-star${i < Math.floor(coach.rating) ? '' : '-o'}"></i>`).join('')}
                                    </div>
                                    <span class="text-sm text-gray-600">${coach.rating} (${coach.sessions} sessions)</span>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <div>
                                    <h4 class="text-sm font-medium text-gray-700">Languages</h4>
                                    <div class="flex flex-wrap gap-1 mt-1">
                                        ${coach.languages.map(lang => Components.createBadge(lang, 'default')).join('')}
                                    </div>
                                </div>

                                <div>
                                    <h4 class="text-sm font-medium text-gray-700">Certifications</h4>
                                    <div class="flex flex-wrap gap-1 mt-1">
                                        ${coach.certifications.map(cert => Components.createBadge(cert, 'success')).join('')}
                                    </div>
                                </div>

                                <div class="flex justify-between items-center pt-3 border-t border-gray-100">
                                    <span class="text-lg font-semibold text-gray-900">$${coach.price}/session</span>
                                    ${Components.createBadge('AI Best Match', 'ai')}
                                </div>

                                <div class="space-y-2">
                                    ${Components.createButton('Book Session', 'primary', 'md', 'fas fa-calendar-plus')}
                                    ${Components.createButton('View Profile', 'outline', 'md', 'fas fa-user')}
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    // Coach/Mentor Pages
    getCoachProfilePage() {
        return `
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Coach Profile</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div class="lg:col-span-1">
                            <div class="text-center">
                                <div class="mx-auto h-32 w-32 rounded-full bg-gray-300 flex items-center justify-center">
                                    <i class="fas fa-user text-4xl text-gray-500"></i>
                                </div>
                                <div class="mt-4 space-y-2">
                                    ${Components.createButton('Upload Photo', 'outline', 'sm', 'fas fa-camera')}
                                    ${Components.createButton('Record Intro Video', 'primary', 'sm', 'fas fa-video')}
                                </div>
                            </div>
                        </div>

                        <div class="lg:col-span-2">
                            <form class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Full Name</label>
                                        <input type="text" value="Sarah Johnson" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Professional Title</label>
                                        <input type="text" value="Senior Leadership Coach" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Years of Experience</label>
                                        <input type="number" value="12" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Hourly Rate ($)</label>
                                        <input type="number" value="120" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Areas of Expertise</label>
                                    ${Components.createDropdown('expertise-select', [
                                        { value: 'leadership', label: 'Leadership & Management' },
                                        { value: 'career', label: 'Career Development' },
                                        { value: 'communication', label: 'Communication Skills' },
                                        { value: 'wellness', label: 'Wellness & Mindfulness' }
                                    ], 'Select expertise areas', true)}
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Languages</label>
                                    ${Components.createDropdown('languages-select', [
                                        { value: 'en', label: 'English' },
                                        { value: 'es', label: 'Spanish' },
                                        { value: 'fr', label: 'French' },
                                        { value: 'de', label: 'German' }
                                    ], 'Select languages', true)}
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Certifications</label>
                                    <textarea rows="3" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" placeholder="List your professional certifications...">ICF Professional Certified Coach (PCC)
Certified Leadership Coach
MBA in Organizational Psychology</textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Bio & Approach</label>
                                    <textarea rows="4" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500" placeholder="Describe your coaching approach and background...">I help leaders unlock their potential through evidence-based coaching methodologies. With over 12 years of experience in executive coaching, I specialize in leadership development, team dynamics, and organizational transformation.</textarea>
                                </div>

                                <div class="flex justify-end space-x-3">
                                    ${Components.createButton('Preview Profile', 'outline')}
                                    ${Components.createButton('Save Changes', 'primary', 'md', 'fas fa-save')}
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    getAvailabilityPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        ${Components.createCard('Availability Calendar', `
                            <div class="mb-4 flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-900">January 2024</h3>
                                <div class="flex space-x-2">
                                    ${Components.createButton('', 'outline', 'sm', 'fas fa-chevron-left')}
                                    ${Components.createButton('', 'outline', 'sm', 'fas fa-chevron-right')}
                                </div>
                            </div>

                            <div class="grid grid-cols-7 gap-1 mb-4">
                                ${['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => `
                                    <div class="text-center text-sm font-medium text-gray-600 py-2">${day}</div>
                                `).join('')}
                                ${Array.from({length: 35}, (_, i) => {
                                    const day = i % 31 + 1;
                                    const isAvailable = Math.random() > 0.3;
                                    const hasBooking = Math.random() > 0.7;
                                    return `
                                        <div class="aspect-square border border-gray-200 p-1 text-center text-sm cursor-pointer hover:bg-gray-50 ${isAvailable ? 'bg-green-50' : ''} ${hasBooking ? 'bg-blue-50' : ''}">
                                            <div class="font-medium">${day}</div>
                                            ${hasBooking ? '<div class="w-2 h-2 bg-blue-500 rounded-full mx-auto mt-1"></div>' : ''}
                                        </div>
                                    `;
                                }).join('')}
                            </div>

                            <div class="flex items-center space-x-4 text-sm">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-green-50 border border-green-200 rounded mr-2"></div>
                                    <span>Available</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-blue-50 border border-blue-200 rounded mr-2"></div>
                                    <span>Booked</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 bg-gray-50 border border-gray-200 rounded mr-2"></div>
                                    <span>Unavailable</span>
                                </div>
                            </div>
                        `)}

                        ${Components.createCard('Time Slots', `
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                ${['9:00 AM', '10:00 AM', '11:00 AM', '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM', '5:00 PM'].map(time => `
                                    <label class="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                        <input type="checkbox" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" checked>
                                        <span class="ml-2 text-sm font-medium text-gray-700">${time}</span>
                                    </label>
                                `).join('')}
                            </div>
                        `)}
                    </div>

                    <div>
                        ${Components.createCard('Calendar Sync', `
                            <div class="space-y-4">
                                <p class="text-sm text-gray-600">Connect your calendar to automatically sync availability and prevent double bookings.</p>

                                <div class="space-y-2">
                                    ${Components.createButton('Connect Google Calendar', 'primary', 'sm', 'fab fa-google')}
                                    ${Components.createButton('Connect Outlook', 'outline', 'sm', 'fab fa-microsoft')}
                                    ${Components.createButton('Connect Apple Calendar', 'outline', 'sm', 'fab fa-apple')}
                                </div>

                                <div class="pt-4 border-t border-gray-200">
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Sync Status</h4>
                                    <div class="flex items-center text-sm text-green-600">
                                        <i class="fas fa-check-circle mr-2"></i>
                                        Google Calendar Connected
                                    </div>
                                </div>
                            </div>
                        `)}

                        ${Components.createCard('Availability Settings', `
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Buffer Time (minutes)</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        <option>15 minutes</option>
                                        <option>30 minutes</option>
                                        <option>45 minutes</option>
                                        <option>60 minutes</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Advance Booking</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        <option>24 hours</option>
                                        <option>48 hours</option>
                                        <option>1 week</option>
                                        <option>2 weeks</option>
                                    </select>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded" checked>
                                    <label class="ml-2 text-sm text-gray-700">Auto-accept bookings</label>
                                </div>
                            </div>
                        `)}
                    </div>
                </div>
            </div>
        `;
    }

    getContentUploadPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        ${Components.createCard('Upload Content', `
                            ${Components.createTabs('content-tabs', [
                                {
                                    id: 'video-upload',
                                    label: 'Videos',
                                    content: `
                                        <div class="space-y-4">
                                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                                                <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                                <p class="text-lg font-medium text-gray-900 mb-2">Upload Video Content</p>
                                                <p class="text-sm text-gray-600 mb-4">Drag and drop your video files here, or click to browse</p>
                                                ${Components.createButton('Choose Files', 'primary', 'md', 'fas fa-folder-open')}
                                                <p class="text-xs text-gray-500 mt-2">Supported formats: MP4, MOV, AVI (Max 500MB)</p>
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Video Title</label>
                                                <input type="text" placeholder="Enter video title..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                                <textarea rows="3" placeholder="Describe your video content..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                                            </div>

                                            <div class="grid grid-cols-2 gap-4">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                        <option>Leadership</option>
                                                        <option>Career Development</option>
                                                        <option>Wellness</option>
                                                        <option>Communication</option>
                                                    </select>
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Access Level</label>
                                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                        <option>Public</option>
                                                        <option>Premium</option>
                                                        <option>Private</option>
                                                    </select>
                                                </div>
                                            </div>

                                            ${Components.createButton('Upload Video', 'primary', 'md', 'fas fa-upload')}
                                        </div>
                                    `
                                },
                                {
                                    id: 'resource-upload',
                                    label: 'Resources',
                                    content: `
                                        <div class="space-y-4">
                                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                                                <i class="fas fa-file-upload text-4xl text-gray-400 mb-4"></i>
                                                <p class="text-lg font-medium text-gray-900 mb-2">Upload Resources</p>
                                                <p class="text-sm text-gray-600 mb-4">PDFs, worksheets, templates, and other materials</p>
                                                ${Components.createButton('Choose Files', 'primary', 'md', 'fas fa-folder-open')}
                                                <p class="text-xs text-gray-500 mt-2">Supported formats: PDF, DOC, DOCX, XLS, XLSX (Max 50MB)</p>
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Resource Title</label>
                                                <input type="text" placeholder="Enter resource title..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                            </div>

                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                                <textarea rows="3" placeholder="Describe your resource..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                                            </div>

                                            ${Components.createButton('Upload Resource', 'primary', 'md', 'fas fa-upload')}
                                        </div>
                                    `
                                },
                                {
                                    id: 'social-connect',
                                    label: 'Social Media',
                                    content: `
                                        <div class="space-y-6">
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900 mb-4">Connect Your Social Media</h3>
                                                <p class="text-sm text-gray-600 mb-6">Import content from your social media profiles to showcase your expertise.</p>

                                                <div class="space-y-4">
                                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                                        <div class="flex items-center">
                                                            <i class="fab fa-linkedin text-2xl text-blue-600 mr-3"></i>
                                                            <div>
                                                                <h4 class="font-medium text-gray-900">LinkedIn</h4>
                                                                <p class="text-sm text-gray-600">Import professional posts and articles</p>
                                                            </div>
                                                        </div>
                                                        ${Components.createButton('Connect', 'primary', 'sm')}
                                                    </div>

                                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                                        <div class="flex items-center">
                                                            <i class="fab fa-instagram text-2xl text-pink-600 mr-3"></i>
                                                            <div>
                                                                <h4 class="font-medium text-gray-900">Instagram</h4>
                                                                <p class="text-sm text-gray-600">Import visual content and stories</p>
                                                            </div>
                                                        </div>
                                                        ${Components.createButton('Connect', 'primary', 'sm')}
                                                    </div>

                                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                                        <div class="flex items-center">
                                                            <i class="fab fa-tiktok text-2xl text-black mr-3"></i>
                                                            <div>
                                                                <h4 class="font-medium text-gray-900">TikTok</h4>
                                                                <p class="text-sm text-gray-600">Import short-form video content</p>
                                                            </div>
                                                        </div>
                                                        ${Components.createButton('Connect', 'primary', 'sm')}
                                                    </div>

                                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                                        <div class="flex items-center">
                                                            <i class="fab fa-google text-2xl text-red-600 mr-3"></i>
                                                            <div>
                                                                <h4 class="font-medium text-gray-900">Google Drive</h4>
                                                                <p class="text-sm text-gray-600">Import documents and presentations</p>
                                                            </div>
                                                        </div>
                                                        ${Components.createButton('Connect', 'primary', 'sm')}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    `
                                }
                            ])}
                        `)}
                    </div>

                    <div>
                        ${Components.createCard('My Content', `
                            <div class="space-y-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary-600">24</div>
                                    <div class="text-sm text-gray-600">Total Videos</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">18</div>
                                    <div class="text-sm text-gray-600">Resources</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">1.2k</div>
                                    <div class="text-sm text-gray-600">Total Views</div>
                                </div>
                            </div>
                        `)}

                        ${Components.createCard('Recent Uploads', `
                            <div class="space-y-3">
                                ${['Leadership Fundamentals', 'Team Communication', 'Goal Setting Workshop'].map(title => `
                                    <div class="flex items-center space-x-3">
                                        <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-play text-primary-600"></i>
                                        </div>
                                        <div class="flex-1">
                                            <div class="text-sm font-medium text-gray-900">${title}</div>
                                            <div class="text-xs text-gray-500">2 days ago</div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `)}
                    </div>
                </div>
            </div>
        `;
    }

    getGroupSessionsPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-900">Group Sessions</h2>
                    ${Components.createButton('Create New Session', 'primary', 'md', 'fas fa-plus')}
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        ${Components.createCard('My Group Sessions', `
                            <div class="space-y-4">
                                ${[
                                    { title: 'Leadership Fundamentals Workshop', date: '2024-01-20', time: '2:00 PM', participants: 12, maxParticipants: 15, status: 'Upcoming' },
                                    { title: 'Career Planning Bootcamp', date: '2024-01-25', time: '10:00 AM', participants: 8, maxParticipants: 10, status: 'Upcoming' },
                                    { title: 'Mindfulness for Leaders', date: '2024-01-15', time: '9:00 AM', participants: 15, maxParticipants: 15, status: 'Completed' }
                                ].map(session => `
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <h3 class="font-semibold text-gray-900">${session.title}</h3>
                                                <p class="text-sm text-gray-600">${session.date} at ${session.time}</p>
                                                <div class="mt-2 flex items-center space-x-4">
                                                    <span class="text-sm text-gray-500">
                                                        <i class="fas fa-users mr-1"></i>
                                                        ${session.participants}/${session.maxParticipants} participants
                                                    </span>
                                                    ${Components.createBadge(session.status, session.status === 'Completed' ? 'success' : 'primary')}
                                                </div>
                                            </div>
                                            <div class="space-x-2">
                                                ${session.status === 'Upcoming'
                                                    ? Components.createButton('Start Session', 'primary', 'sm', 'fas fa-video')
                                                    : Components.createButton('View Recording', 'outline', 'sm', 'fas fa-play')
                                                }
                                                ${Components.createButton('', 'outline', 'sm', 'fas fa-ellipsis-h')}
                                            </div>
                                        </div>

                                        <div class="mt-4 pt-4 border-t border-gray-100">
                                            ${Components.createProgressBar((session.participants / session.maxParticipants) * 100)}
                                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                                <span>Enrollment</span>
                                                <span>${Math.round((session.participants / session.maxParticipants) * 100)}% full</span>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `)}
                    </div>

                    <div>
                        ${Components.createCard('Session Stats', `
                            <div class="space-y-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary-600">8</div>
                                    <div class="text-sm text-gray-600">Active Sessions</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-600">156</div>
                                    <div class="text-sm text-gray-600">Total Participants</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-blue-600">4.8</div>
                                    <div class="text-sm text-gray-600">Average Rating</div>
                                </div>
                            </div>
                        `)}

                        ${Components.createCard('Quick Actions', `
                            <div class="space-y-3">
                                ${Components.createButton('Create Workshop', 'primary', 'sm', 'fas fa-plus')}
                                ${Components.createButton('Schedule Recurring', 'outline', 'sm', 'fas fa-repeat')}
                                ${Components.createButton('Import from Calendar', 'outline', 'sm', 'fas fa-calendar-import')}
                                ${Components.createButton('View Analytics', 'outline', 'sm', 'fas fa-chart-bar')}
                            </div>
                        `)}
                    </div>
                </div>

                <!-- Create Session Modal -->
                ${Components.createModal('create-session-modal', 'Create New Group Session', `
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Session Title</label>
                            <input type="text" placeholder="Enter session title..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                                ${Components.createCalendarPicker('session-date')}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Time</label>
                                <input type="time" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Duration (hours)</label>
                                <input type="number" value="2" min="1" max="8" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Max Participants</label>
                                <input type="number" value="15" min="2" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea rows="3" placeholder="Describe your session..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                        </div>

                        <div class="flex justify-end space-x-3">
                            ${Components.createButton('Cancel', 'secondary', 'md', '', 'Components.closeModal("create-session-modal")')}
                            ${Components.createButton('Create Session', 'primary', 'md', 'fas fa-plus')}
                        </div>
                    </form>
                `, 'lg')}
            </div>
        `;
    }

    getPayoutPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div class="lg:col-span-3">
                        ${Components.createCard('Earnings Overview', `
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                                <div class="text-center p-4 bg-green-50 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">$2,450</div>
                                    <div class="text-sm text-gray-600">This Month</div>
                                </div>
                                <div class="text-center p-4 bg-blue-50 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">$8,920</div>
                                    <div class="text-sm text-gray-600">Total Earned</div>
                                </div>
                                <div class="text-center p-4 bg-yellow-50 rounded-lg">
                                    <div class="text-2xl font-bold text-yellow-600">$1,200</div>
                                    <div class="text-sm text-gray-600">Pending</div>
                                </div>
                                <div class="text-center p-4 bg-purple-50 rounded-lg">
                                    <div class="text-2xl font-bold text-purple-600">$7,720</div>
                                    <div class="text-sm text-gray-600">Available</div>
                                </div>
                            </div>

                            <div class="border-t border-gray-200 pt-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Transactions</h3>
                                <div class="space-y-3">
                                    ${[
                                        { type: 'Session', client: 'John Doe', amount: 120, date: '2024-01-15', status: 'Completed' },
                                        { type: 'Workshop', client: 'Leadership Group', amount: 300, date: '2024-01-14', status: 'Completed' },
                                        { type: 'Session', client: 'Sarah Smith', amount: 120, date: '2024-01-13', status: 'Pending' },
                                        { type: 'Session', client: 'Mike Johnson', amount: 120, date: '2024-01-12', status: 'Completed' }
                                    ].map(transaction => `
                                        <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-${transaction.type === 'Session' ? 'user' : 'users'} text-primary-600"></i>
                                                </div>
                                                <div>
                                                    <div class="font-medium text-gray-900">${transaction.type} - ${transaction.client}</div>
                                                    <div class="text-sm text-gray-600">${transaction.date}</div>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <div class="font-semibold text-gray-900">$${transaction.amount}</div>
                                                ${Components.createBadge(transaction.status, transaction.status === 'Completed' ? 'success' : 'warning')}
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        `)}
                    </div>

                    <div>
                        ${Components.createCard('Payout Settings', `
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        <option>Bank Transfer</option>
                                        <option>PayPal</option>
                                        <option>Stripe</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Payout Frequency</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        <option>Weekly</option>
                                        <option>Bi-weekly</option>
                                        <option>Monthly</option>
                                    </select>
                                </div>

                                <div class="pt-4 border-t border-gray-200">
                                    ${Components.createButton('Request Payout', 'primary', 'sm', 'fas fa-money-bill-wave')}
                                    ${Components.createButton('Update Settings', 'outline', 'sm', 'fas fa-cog')}
                                </div>
                            </div>
                        `)}

                        ${Components.createCard('Tax Information', `
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Tax Year 2024</span>
                                    <span class="text-sm font-medium text-gray-900">$8,920</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">1099 Status</span>
                                    <span class="text-sm text-green-600">Ready</span>
                                </div>
                                <div class="pt-3 border-t border-gray-200">
                                    ${Components.createButton('Download 1099', 'outline', 'sm', 'fas fa-download')}
                                </div>
                            </div>
                        `)}
                    </div>
                </div>
            </div>
        `;
    }

    // Corporate Admin Pages
    getCorporateDashboardPage() {
        return `
            <div class="max-w-7xl mx-auto space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-blue-100">
                                <i class="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Active Employees</p>
                                <p class="text-2xl font-semibold text-gray-900">1,247</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-green-600 text-sm font-medium">+12% from last month</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-green-100">
                                <i class="fas fa-calendar-check text-green-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Sessions Booked</p>
                                <p class="text-2xl font-semibold text-gray-900">3,456</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-green-600 text-sm font-medium">+8% from last month</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-purple-100">
                                <i class="fas fa-chalkboard-teacher text-purple-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Coaches Engaged</p>
                                <p class="text-2xl font-semibold text-gray-900">89</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-green-600 text-sm font-medium">+5% from last month</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center">
                            <div class="p-3 rounded-full bg-yellow-100">
                                <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-600">Engagement Score</p>
                                <p class="text-2xl font-semibold text-gray-900">87%</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <span class="text-green-600 text-sm font-medium">+3% from last month</span>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    ${Components.createCard('Recent Activity', `
                        <div class="space-y-4">
                            ${[
                                { user: 'John Smith', action: 'completed a leadership session', time: '2 hours ago', icon: 'fas fa-check-circle', color: 'text-green-600' },
                                { user: 'Sarah Johnson', action: 'joined the Wellness Circle', time: '4 hours ago', icon: 'fas fa-users', color: 'text-blue-600' },
                                { user: 'Mike Davis', action: 'booked a career coaching session', time: '6 hours ago', icon: 'fas fa-calendar-plus', color: 'text-purple-600' },
                                { user: 'Emily Chen', action: 'completed an e-learning module', time: '8 hours ago', icon: 'fas fa-play-circle', color: 'text-indigo-600' }
                            ].map(activity => `
                                <div class="flex items-center space-x-3">
                                    <i class="${activity.icon} ${activity.color}"></i>
                                    <div class="flex-1">
                                        <p class="text-sm text-gray-900">
                                            <span class="font-medium">${activity.user}</span> ${activity.action}
                                        </p>
                                        <p class="text-xs text-gray-500">${activity.time}</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `)}

                    ${Components.createCard('Top Performing Departments', `
                        <div class="space-y-4">
                            ${[
                                { dept: 'Engineering', score: 92, sessions: 156 },
                                { dept: 'Sales', score: 88, sessions: 134 },
                                { dept: 'Marketing', score: 85, sessions: 98 },
                                { dept: 'HR', score: 82, sessions: 76 }
                            ].map(dept => `
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">${dept.dept}</p>
                                        <p class="text-sm text-gray-600">${dept.sessions} sessions completed</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-semibold text-gray-900">${dept.score}%</p>
                                        <div class="w-20 bg-gray-200 rounded-full h-2">
                                            <div class="bg-primary-600 h-2 rounded-full" style="width: ${dept.score}%"></div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `)}
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    ${Components.createCard('Quick Actions', `
                        <div class="grid grid-cols-2 gap-3">
                            ${Components.createButton('Invite Employees', 'primary', 'sm', 'fas fa-user-plus')}
                            ${Components.createButton('View Analytics', 'outline', 'sm', 'fas fa-chart-bar')}
                            ${Components.createButton('Manage Coaches', 'outline', 'sm', 'fas fa-users-cog')}
                            ${Components.createButton('Export Data', 'outline', 'sm', 'fas fa-download')}
                        </div>
                    `)}

                    ${Components.createCard('Upcoming Events', `
                        <div class="space-y-3">
                            ${[
                                { title: 'Leadership Workshop', date: 'Jan 25', participants: 15 },
                                { title: 'Wellness Seminar', date: 'Jan 28', participants: 23 },
                                { title: 'Career Fair', date: 'Feb 2', participants: 45 }
                            ].map(event => `
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">${event.title}</p>
                                        <p class="text-xs text-gray-600">${event.date}</p>
                                    </div>
                                    <span class="text-xs text-gray-500">${event.participants} attending</span>
                                </div>
                            `).join('')}
                        </div>
                    `)}

                    ${Components.createCard('System Health', `
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Platform Uptime</span>
                                <span class="text-sm font-medium text-green-600">99.9%</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Active Sessions</span>
                                <span class="text-sm font-medium text-blue-600">47</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">Response Time</span>
                                <span class="text-sm font-medium text-gray-900">120ms</span>
                            </div>
                        </div>
                    `)}
                </div>
            </div>
        `;
    }

    getEmployeeManagementPage() {
        const sampleEmployees = [
            { id: 1, name: 'John Smith', email: '<EMAIL>', department: 'Engineering', role: 'Senior Developer', status: 'Active', joinDate: '2023-03-15', sessions: 12 },
            { id: 2, name: 'Sarah Johnson', email: '<EMAIL>', department: 'Marketing', role: 'Marketing Manager', status: 'Active', joinDate: '2023-01-20', sessions: 8 },
            { id: 3, name: 'Mike Davis', email: '<EMAIL>', department: 'Sales', role: 'Sales Representative', status: 'Active', joinDate: '2023-06-10', sessions: 15 },
            { id: 4, name: 'Emily Chen', email: '<EMAIL>', department: 'HR', role: 'HR Specialist', status: 'Inactive', joinDate: '2023-02-28', sessions: 5 }
        ];

        return `
            <div class="max-w-7xl mx-auto space-y-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-900">Employee Management</h2>
                    <div class="flex space-x-3">
                        ${Components.createButton('Invite Employees', 'primary', 'md', 'fas fa-user-plus', 'Components.openModal("invite-modal")')}
                        ${Components.createButton('Export List', 'outline', 'md', 'fas fa-download')}
                    </div>
                </div>

                ${Components.createCard('Employee List', `
                    <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                        <div class="flex space-x-3">
                            <input type="text" placeholder="Search employees..." class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option>All Departments</option>
                                <option>Engineering</option>
                                <option>Marketing</option>
                                <option>Sales</option>
                                <option>HR</option>
                            </select>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">Show:</span>
                            <select class="px-2 py-1 border border-gray-300 rounded text-sm">
                                <option>10</option>
                                <option>25</option>
                                <option>50</option>
                            </select>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                ${sampleEmployees.map(employee => `
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                ${Components.createAvatar('', employee.name, 'sm')}
                                                <div class="ml-3">
                                                    <div class="text-sm font-medium text-gray-900">${employee.name}</div>
                                                    <div class="text-sm text-gray-500">${employee.email}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.department}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.role}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.sessions}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            ${Components.createBadge(employee.status, employee.status === 'Active' ? 'success' : 'default')}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            ${Components.createButton('View', 'outline', 'sm', 'fas fa-eye')}
                                            ${Components.createButton('Edit', 'outline', 'sm', 'fas fa-edit')}
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing 1 to 4 of 1,247 employees
                        </div>
                        <div class="flex space-x-2">
                            ${Components.createButton('Previous', 'outline', 'sm')}
                            ${Components.createButton('Next', 'outline', 'sm')}
                        </div>
                    </div>
                `)}

                <!-- Invite Employees Modal -->
                ${Components.createModal('invite-modal', 'Invite Employees', `
                    <form class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Invitation Method</label>
                            ${Components.createTabs('invite-tabs', [
                                {
                                    id: 'email-invite',
                                    label: 'Email Addresses',
                                    content: `
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Email Addresses</label>
                                                <textarea rows="4" placeholder="Enter email addresses, one per line or separated by commas..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Default Department</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                    <option>Select Department</option>
                                                    <option>Engineering</option>
                                                    <option>Marketing</option>
                                                    <option>Sales</option>
                                                    <option>HR</option>
                                                </select>
                                            </div>
                                        </div>
                                    `
                                },
                                {
                                    id: 'csv-upload',
                                    label: 'CSV Upload',
                                    content: `
                                        <div class="space-y-4">
                                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                                <i class="fas fa-file-csv text-3xl text-gray-400 mb-3"></i>
                                                <p class="text-sm text-gray-600 mb-3">Upload a CSV file with employee information</p>
                                                ${Components.createButton('Choose File', 'outline', 'sm', 'fas fa-upload')}
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                <p>CSV should include columns: Name, Email, Department, Role</p>
                                                <a href="#" class="text-primary-600 hover:text-primary-500">Download sample CSV template</a>
                                            </div>
                                        </div>
                                    `
                                }
                            ])}
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Custom Message (Optional)</label>
                            <textarea rows="3" placeholder="Add a personal message to the invitation..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                        </div>

                        <div class="flex justify-end space-x-3">
                            ${Components.createButton('Cancel', 'secondary', 'md', '', 'Components.closeModal("invite-modal")')}
                            ${Components.createButton('Send Invitations', 'primary', 'md', 'fas fa-paper-plane')}
                        </div>
                    </form>
                `, 'lg')}
            </div>
        `;
    }

    getAnalyticsPage() {
        return `
            <div class="max-w-7xl mx-auto space-y-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-900">Analytics Dashboard</h2>
                    <div class="flex space-x-3">
                        <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option>Last 30 days</option>
                            <option>Last 90 days</option>
                            <option>Last 6 months</option>
                            <option>Last year</option>
                        </select>
                        ${Components.createButton('Export Report', 'outline', 'md', 'fas fa-download')}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Total Sessions</p>
                                <p class="text-2xl font-semibold text-gray-900">3,456</p>
                            </div>
                            <div class="p-3 rounded-full bg-blue-100">
                                <i class="fas fa-calendar-check text-blue-600"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <span class="text-green-600 text-sm font-medium">+12.5%</span>
                            <span class="text-gray-600 text-sm ml-2">vs last period</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Avg. Engagement</p>
                                <p class="text-2xl font-semibold text-gray-900">87%</p>
                            </div>
                            <div class="p-3 rounded-full bg-green-100">
                                <i class="fas fa-chart-line text-green-600"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <span class="text-green-600 text-sm font-medium">+3.2%</span>
                            <span class="text-gray-600 text-sm ml-2">vs last period</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Completion Rate</p>
                                <p class="text-2xl font-semibold text-gray-900">92%</p>
                            </div>
                            <div class="p-3 rounded-full bg-purple-100">
                                <i class="fas fa-check-circle text-purple-600"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <span class="text-green-600 text-sm font-medium">+5.1%</span>
                            <span class="text-gray-600 text-sm ml-2">vs last period</span>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Satisfaction</p>
                                <p class="text-2xl font-semibold text-gray-900">4.8/5</p>
                            </div>
                            <div class="p-3 rounded-full bg-yellow-100">
                                <i class="fas fa-star text-yellow-600"></i>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <span class="text-green-600 text-sm font-medium">+0.3</span>
                            <span class="text-gray-600 text-sm ml-2">vs last period</span>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    ${Components.createCard('User Engagement Trends', `
                        <div class="h-64">
                            <canvas id="engagement-chart"></canvas>
                        </div>
                    `)}

                    ${Components.createCard('Session Distribution', `
                        <div class="h-64">
                            <canvas id="session-chart"></canvas>
                        </div>
                    `)}
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    ${Components.createCard('Top Performing Content', `
                        <div class="space-y-4">
                            ${[
                                { title: 'Leadership Fundamentals', views: 1234, rating: 4.9 },
                                { title: 'Effective Communication', views: 987, rating: 4.8 },
                                { title: 'Time Management', views: 856, rating: 4.7 },
                                { title: 'Team Building', views: 743, rating: 4.6 }
                            ].map(content => `
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="font-medium text-gray-900">${content.title}</p>
                                        <p class="text-sm text-gray-600">${content.views} views</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="flex items-center">
                                            <i class="fas fa-star text-yellow-400 mr-1"></i>
                                            <span class="text-sm font-medium">${content.rating}</span>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `)}

                    ${Components.createCard('Department Performance', `
                        <div class="space-y-4">
                            ${[
                                { dept: 'Engineering', score: 92, change: '+5%' },
                                { dept: 'Sales', score: 88, change: '+3%' },
                                { dept: 'Marketing', score: 85, change: '+2%' },
                                { dept: 'HR', score: 82, change: '+1%' }
                            ].map(dept => `
                                <div class="flex justify-between items-center">
                                    <div>
                                        <p class="font-medium text-gray-900">${dept.dept}</p>
                                        <p class="text-sm text-gray-600">Engagement Score</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="font-semibold text-gray-900">${dept.score}%</p>
                                        <p class="text-sm text-green-600">${dept.change}</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `)}

                    ${Components.createCard('Recent Insights', `
                        <div class="space-y-3">
                            ${[
                                { insight: 'Leadership sessions show 15% higher completion rates', type: 'positive' },
                                { insight: 'Mobile usage increased by 23% this month', type: 'info' },
                                { insight: 'Weekend sessions have lower engagement', type: 'warning' },
                                { insight: 'AI recommendations improved match accuracy by 12%', type: 'positive' }
                            ].map(item => `
                                <div class="flex items-start space-x-2">
                                    <i class="fas fa-${item.type === 'positive' ? 'arrow-up text-green-500' : item.type === 'warning' ? 'exclamation-triangle text-yellow-500' : 'info-circle text-blue-500'} mt-1"></i>
                                    <p class="text-sm text-gray-700">${item.insight}</p>
                                </div>
                            `).join('')}
                        </div>
                    `)}
                </div>
            </div>
        `;
    }

    // AI Features Pages
    getAICoachMatchingPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">AI Coach Matching</h2>
                    <p class="text-lg text-gray-600">Let our AI find the perfect coach based on your goals, preferences, and learning style</p>
                </div>

                ${Components.createCard('Your Preferences', `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Primary Goal</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option>Leadership Development</option>
                                <option>Career Advancement</option>
                                <option>Wellness & Balance</option>
                                <option>Communication Skills</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Communication Style</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option>Direct & Structured</option>
                                <option>Supportive & Encouraging</option>
                                <option>Challenging & Analytical</option>
                                <option>Creative & Flexible</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Experience Level</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option>Entry Level (0-2 years)</option>
                                <option>Mid Level (3-7 years)</option>
                                <option>Senior Level (8-15 years)</option>
                                <option>Executive Level (15+ years)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Budget Range</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                <option>$50-$100 per session</option>
                                <option>$100-$150 per session</option>
                                <option>$150-$200 per session</option>
                                <option>$200+ per session</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-6 text-center">
                        ${Components.createButton('Find My Perfect Match', 'primary', 'lg', 'fas fa-magic')}
                    </div>
                `)}

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    ${this.sampleData.coaches.map((coach, index) => `
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 relative">
                            ${index === 0 ? `<div class="absolute -top-3 -right-3">${Components.createBadge('Best Match', 'ai')}</div>` : ''}
                            <div class="text-center mb-4">
                                ${Components.createAvatar(coach.image, coach.name, 'xl')}
                                <h3 class="mt-3 font-semibold text-gray-900">${coach.name}</h3>
                                <p class="text-sm text-gray-600">${coach.expertise}</p>
                                <div class="mt-2 flex items-center justify-center space-x-1">
                                    <div class="flex text-yellow-400">
                                        ${Array(5).fill().map((_, i) => `<i class="fas fa-star${i < Math.floor(coach.rating) ? '' : '-o'}"></i>`).join('')}
                                    </div>
                                    <span class="text-sm text-gray-600">${coach.rating}</span>
                                </div>
                            </div>

                            <div class="space-y-3 mb-4">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Match Score:</span>
                                    <span class="font-medium text-primary-600">${95 - index * 5}%</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Sessions:</span>
                                    <span class="font-medium">${coach.sessions}</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Rate:</span>
                                    <span class="font-medium">$${coach.price}/session</span>
                                </div>
                            </div>

                            <div class="space-y-2">
                                ${Components.createButton('Book Session', 'primary', 'sm', 'fas fa-calendar-plus')}
                                ${Components.createButton('View Profile', 'outline', 'sm', 'fas fa-user')}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    getAIRecommendationsPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">AI Recommendations</h2>
                    <p class="text-lg text-gray-600">Personalized content and learning paths powered by AI</p>
                </div>

                ${Components.createCard('Recommended for You', `
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        ${[
                            { type: 'Course', title: 'Advanced Leadership Strategies', reason: 'Based on your leadership goals', icon: 'fas fa-graduation-cap', color: 'blue' },
                            { type: 'Book', title: 'The Psychology of Influence', reason: 'Matches your communication interests', icon: 'fas fa-book', color: 'green' },
                            { type: 'Workshop', title: 'Mindful Leadership Retreat', reason: 'Combines leadership and wellness', icon: 'fas fa-users', color: 'purple' },
                            { type: 'Podcast', title: 'Future of Work Series', reason: 'Trending in your industry', icon: 'fas fa-podcast', color: 'red' },
                            { type: 'Article', title: 'Remote Team Management', reason: 'Based on your recent searches', icon: 'fas fa-newspaper', color: 'yellow' },
                            { type: 'Video', title: 'Emotional Intelligence Masterclass', reason: 'High engagement in your network', icon: 'fas fa-play-circle', color: 'indigo' }
                        ].map(item => `
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center mb-3">
                                    <div class="w-10 h-10 bg-${item.color}-100 rounded-lg flex items-center justify-center">
                                        <i class="${item.icon} text-${item.color}-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        ${Components.createBadge(item.type, 'ai')}
                                    </div>
                                </div>
                                <h4 class="font-semibold text-gray-900 mb-2">${item.title}</h4>
                                <p class="text-sm text-gray-600 mb-3">${item.reason}</p>
                                <div class="flex space-x-2">
                                    ${Components.createButton('View', 'primary', 'sm')}
                                    ${Components.createButton('Save', 'outline', 'sm', 'fas fa-bookmark')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `)}

                ${Components.createCard('Learning Path Suggestions', `
                    <div class="space-y-4">
                        ${[
                            { title: 'Executive Leadership Track', duration: '12 weeks', modules: 8, description: 'Comprehensive leadership development for senior roles' },
                            { title: 'Digital Transformation Leader', duration: '8 weeks', modules: 6, description: 'Lead change in the digital age' },
                            { title: 'Wellness-Focused Leadership', duration: '6 weeks', modules: 5, description: 'Balance performance with well-being' }
                        ].map(path => `
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-semibold text-gray-900">${path.title}</h4>
                                        <p class="text-sm text-gray-600 mt-1">${path.description}</p>
                                        <div class="flex items-center mt-2 space-x-4">
                                            <span class="text-sm text-gray-500">
                                                <i class="fas fa-clock mr-1"></i>
                                                ${path.duration}
                                            </span>
                                            <span class="text-sm text-gray-500">
                                                <i class="fas fa-layer-group mr-1"></i>
                                                ${path.modules} modules
                                            </span>
                                        </div>
                                    </div>
                                    <div class="space-x-2">
                                        ${Components.createButton('Start Path', 'primary', 'sm')}
                                        ${Components.createButton('Preview', 'outline', 'sm')}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `)}

                ${Components.createCard('AI Insights', `
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Your Learning Pattern</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Best Learning Time:</span>
                                    <span class="text-sm font-medium">Morning (9-11 AM)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Preferred Format:</span>
                                    <span class="text-sm font-medium">Interactive Videos</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Completion Rate:</span>
                                    <span class="text-sm font-medium">87%</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-900 mb-3">Trending in Your Network</h4>
                            <div class="space-y-2">
                                ${['Hybrid Work Leadership', 'AI in Management', 'Sustainable Business Practices'].map(trend => `
                                    <div class="flex items-center">
                                        <i class="fas fa-trending-up text-green-500 mr-2"></i>
                                        <span class="text-sm text-gray-700">${trend}</span>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `)}
            </div>
        `;
    }

    getPaymentsPage() {
        return `
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
                    <p class="text-lg text-gray-600">Select the perfect plan for your professional development journey</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="text-center">
                            <h3 class="text-xl font-semibold text-gray-900">Free</h3>
                            <div class="mt-4">
                                <span class="text-4xl font-bold text-gray-900">$0</span>
                                <span class="text-gray-600">/month</span>
                            </div>
                        </div>
                        <ul class="mt-6 space-y-3">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Access to basic content</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Community access</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">1 free session per month</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-times text-gray-400 mr-3"></i>
                                <span class="text-sm text-gray-400">Premium content</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-times text-gray-400 mr-3"></i>
                                <span class="text-sm text-gray-400">AI recommendations</span>
                            </li>
                        </ul>
                        <div class="mt-6">
                            ${Components.createButton('Get Started', 'outline', 'lg')}
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg border-2 border-primary-500 p-6 relative">
                        <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                            ${Components.createBadge('Most Popular', 'primary')}
                        </div>
                        <div class="text-center">
                            <h3 class="text-xl font-semibold text-gray-900">Pay-as-you-Go</h3>
                            <div class="mt-4">
                                <span class="text-4xl font-bold text-gray-900">$25</span>
                                <span class="text-gray-600">/session</span>
                            </div>
                        </div>
                        <ul class="mt-6 space-y-3">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">All free features</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Premium content access</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Unlimited sessions</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">AI coach matching</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Priority support</span>
                            </li>
                        </ul>
                        <div class="mt-6">
                            ${Components.createButton('Choose Plan', 'primary', 'lg')}
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                        <div class="text-center">
                            <h3 class="text-xl font-semibold text-gray-900">Subscription</h3>
                            <div class="mt-4">
                                <span class="text-4xl font-bold text-gray-900">$99</span>
                                <span class="text-gray-600">/month</span>
                            </div>
                        </div>
                        <ul class="mt-6 space-y-3">
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">All pay-as-you-go features</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Unlimited sessions</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Personal learning path</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Advanced analytics</span>
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <span class="text-sm text-gray-700">Dedicated account manager</span>
                            </li>
                        </ul>
                        <div class="mt-6">
                            ${Components.createButton('Choose Plan', 'outline', 'lg')}
                        </div>
                    </div>
                </div>

                ${Components.createCard('Enterprise Solutions', `
                    <div class="text-center">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">Custom Enterprise Plans</h3>
                        <p class="text-gray-600 mb-6">Tailored solutions for organizations with 100+ employees</p>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div class="text-center">
                                <i class="fas fa-users text-3xl text-primary-600 mb-2"></i>
                                <h4 class="font-semibold text-gray-900">Unlimited Users</h4>
                                <p class="text-sm text-gray-600">Scale across your entire organization</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-cog text-3xl text-primary-600 mb-2"></i>
                                <h4 class="font-semibold text-gray-900">Custom Integration</h4>
                                <p class="text-sm text-gray-600">Seamless integration with your systems</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-headset text-3xl text-primary-600 mb-2"></i>
                                <h4 class="font-semibold text-gray-900">Dedicated Support</h4>
                                <p class="text-sm text-gray-600">24/7 support and account management</p>
                            </div>
                        </div>
                        ${Components.createButton('Contact Sales', 'primary', 'lg', 'fas fa-phone')}
                    </div>
                `)}
            </div>
        `;
    }

    getSettingsPage() {
        return `
            <div class="max-w-4xl mx-auto space-y-6">
                <h2 class="text-2xl font-bold text-gray-900">Settings</h2>

                ${Components.createTabs('settings-tabs', [
                    {
                        id: 'profile-settings',
                        label: 'Profile',
                        content: `
                            <div class="space-y-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                            <input type="text" value="John" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                            <input type="text" value="Doe" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                            <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                            <input type="tel" value="+****************" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Change Password</h3>
                                    <div class="space-y-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                                            <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                                            <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                                            <input type="password" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `
                    },
                    {
                        id: 'notification-settings',
                        label: 'Notifications',
                        content: `
                            <div class="space-y-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Email Notifications</h3>
                                    <div class="space-y-3">
                                        ${[
                                            { label: 'Session reminders', description: 'Get notified before your scheduled sessions' },
                                            { label: 'New recommendations', description: 'Receive AI-powered content recommendations' },
                                            { label: 'Community updates', description: 'Stay updated on community discussions' },
                                            { label: 'Weekly progress reports', description: 'Get weekly summaries of your learning progress' }
                                        ].map(item => `
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <p class="font-medium text-gray-900">${item.label}</p>
                                                    <p class="text-sm text-gray-600">${item.description}</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                                                </label>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Push Notifications</h3>
                                    <div class="space-y-3">
                                        ${[
                                            { label: 'Session starting soon', description: 'Get notified 15 minutes before sessions' },
                                            { label: 'New messages', description: 'Receive notifications for new messages' },
                                            { label: 'Achievement unlocked', description: 'Celebrate your learning milestones' }
                                        ].map(item => `
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <p class="font-medium text-gray-900">${item.label}</p>
                                                    <p class="text-sm text-gray-600">${item.description}</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                                                </label>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        `
                    },
                    {
                        id: 'language-settings',
                        label: 'Language & Region',
                        content: `
                            <div class="space-y-6">
                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Language Preferences</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Interface Language</label>
                                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                <option>English (US)</option>
                                                <option>English (UK)</option>
                                                <option>Spanish</option>
                                                <option>French</option>
                                                <option>German</option>
                                                <option>Portuguese</option>
                                                <option>Chinese (Simplified)</option>
                                                <option>Japanese</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Content Language</label>
                                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                <option>English</option>
                                                <option>Spanish</option>
                                                <option>French</option>
                                                <option>German</option>
                                                <option>Portuguese</option>
                                                <option>Chinese</option>
                                                <option>Japanese</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Regional Settings</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                <option>Pacific Time (PT)</option>
                                                <option>Mountain Time (MT)</option>
                                                <option>Central Time (CT)</option>
                                                <option>Eastern Time (ET)</option>
                                                <option>UTC</option>
                                                <option>Central European Time (CET)</option>
                                                <option>Japan Standard Time (JST)</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                                <option>MM/DD/YYYY</option>
                                                <option>DD/MM/YYYY</option>
                                                <option>YYYY-MM-DD</option>
                                                <option>DD.MM.YYYY</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `
                    }
                ])}

                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                    ${Components.createButton('Cancel', 'secondary')}
                    ${Components.createButton('Save Changes', 'primary', 'md', 'fas fa-save')}
                </div>
            </div>
        `;
    }

    // Additional Corporate Admin Pages
    getBrandedCommunitiesPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-900">Branded Communities</h2>
                    ${Components.createButton('Create Community', 'primary', 'md', 'fas fa-plus')}
                </div>

                ${Components.createCard('Community Builder', `
                    <form class="space-y-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Community Name</label>
                                    <input type="text" placeholder="e.g., Tech Corp Leadership Circle" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                    <textarea rows="3" placeholder="Describe the purpose and goals of this community..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500"></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        <option>Leadership Development</option>
                                        <option>Technical Skills</option>
                                        <option>Wellness & Balance</option>
                                        <option>Career Growth</option>
                                        <option>Innovation & Creativity</option>
                                    </select>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Access Level</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                                        <option>All Employees</option>
                                        <option>Department Specific</option>
                                        <option>Management Only</option>
                                        <option>Invite Only</option>
                                    </select>
                                </div>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Community Logo</label>
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                        <i class="fas fa-image text-3xl text-gray-400 mb-2"></i>
                                        <p class="text-sm text-gray-600">Upload community logo</p>
                                        ${Components.createButton('Choose File', 'outline', 'sm', 'fas fa-upload')}
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Brand Colors</label>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Primary Color</label>
                                            <input type="color" value="#3b82f6" class="w-full h-10 border border-gray-300 rounded-md">
                                        </div>
                                        <div>
                                            <label class="block text-xs text-gray-600 mb-1">Secondary Color</label>
                                            <input type="color" value="#10b981" class="w-full h-10 border border-gray-300 rounded-md">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3">
                            ${Components.createButton('Preview', 'outline')}
                            ${Components.createButton('Create Community', 'primary', 'md', 'fas fa-plus')}
                        </div>
                    </form>
                `)}

                ${Components.createCard('Existing Communities', `
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        ${[
                            { name: 'Leadership Excellence', members: 45, posts: 128, category: 'Leadership' },
                            { name: 'Innovation Hub', members: 67, posts: 89, category: 'Innovation' },
                            { name: 'Wellness Warriors', members: 34, posts: 156, category: 'Wellness' },
                            { name: 'Tech Talks', members: 89, posts: 234, category: 'Technical' }
                        ].map(community => `
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center mb-3">
                                    <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-users text-primary-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="font-semibold text-gray-900">${community.name}</h4>
                                        ${Components.createBadge(community.category, 'default')}
                                    </div>
                                </div>

                                <div class="space-y-2 mb-4">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Members:</span>
                                        <span class="font-medium">${community.members}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Posts:</span>
                                        <span class="font-medium">${community.posts}</span>
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    ${Components.createButton('Manage', 'primary', 'sm')}
                                    ${Components.createButton('Analytics', 'outline', 'sm', 'fas fa-chart-bar')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `)}
            </div>
        `;
    }

    getEnterpriseBillingPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-900">Enterprise Billing</h2>
                    ${Components.createButton('Download Invoice', 'outline', 'md', 'fas fa-download')}
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        ${Components.createCard('Current Subscription', `
                            <div class="flex justify-between items-start mb-6">
                                <div>
                                    <h3 class="text-xl font-semibold text-gray-900">Enterprise Plan</h3>
                                    <p class="text-gray-600">1,247 active users</p>
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-gray-900">$12,470</div>
                                    <div class="text-sm text-gray-600">per month</div>
                                </div>
                            </div>

                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <div class="text-center p-4 bg-blue-50 rounded-lg">
                                    <div class="text-lg font-semibold text-blue-600">$149,640</div>
                                    <div class="text-sm text-gray-600">Annual Total</div>
                                </div>
                                <div class="text-center p-4 bg-green-50 rounded-lg">
                                    <div class="text-lg font-semibold text-green-600">$37,410</div>
                                    <div class="text-sm text-gray-600">Annual Savings</div>
                                </div>
                            </div>

                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Next billing date:</span>
                                    <span class="font-medium">February 15, 2024</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Payment method:</span>
                                    <span class="font-medium">•••• •••• •••• 4242</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Billing contact:</span>
                                    <span class="font-medium"><EMAIL></span>
                                </div>
                            </div>
                        `)}

                        ${Components.createCard('Usage Analytics', `
                            <div class="grid grid-cols-3 gap-4 mb-6">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-900">3,456</div>
                                    <div class="text-sm text-gray-600">Sessions This Month</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-900">87%</div>
                                    <div class="text-sm text-gray-600">User Adoption</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-gray-900">$10.15</div>
                                    <div class="text-sm text-gray-600">Cost per User</div>
                                </div>
                            </div>

                            <div class="h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                                <span class="text-gray-500">Usage chart placeholder</span>
                            </div>
                        `)}
                    </div>

                    <div>
                        ${Components.createCard('Billing History', `
                            <div class="space-y-4">
                                ${[
                                    { date: 'Jan 15, 2024', amount: '$12,470', status: 'Paid' },
                                    { date: 'Dec 15, 2023', amount: '$12,470', status: 'Paid' },
                                    { date: 'Nov 15, 2023', amount: '$11,890', status: 'Paid' },
                                    { date: 'Oct 15, 2023', amount: '$11,890', status: 'Paid' }
                                ].map(invoice => `
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <div class="font-medium text-gray-900">${invoice.amount}</div>
                                            <div class="text-sm text-gray-600">${invoice.date}</div>
                                        </div>
                                        <div class="text-right">
                                            ${Components.createBadge(invoice.status, 'success')}
                                            <div class="mt-1">
                                                ${Components.createButton('', 'outline', 'sm', 'fas fa-download')}
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        `)}

                        ${Components.createCard('Payment Settings', `
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                                    <div class="flex items-center p-3 border border-gray-200 rounded-lg">
                                        <i class="fab fa-cc-visa text-2xl text-blue-600 mr-3"></i>
                                        <div>
                                            <div class="font-medium">•••• •••• •••• 4242</div>
                                            <div class="text-sm text-gray-600">Expires 12/25</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="space-y-2">
                                    ${Components.createButton('Update Payment', 'outline', 'sm', 'fas fa-credit-card')}
                                    ${Components.createButton('Billing Address', 'outline', 'sm', 'fas fa-map-marker-alt')}
                                    ${Components.createButton('Tax Settings', 'outline', 'sm', 'fas fa-receipt')}
                                </div>
                            </div>
                        `)}
                    </div>
                </div>
            </div>
        `;
    }

    getIntegrationsPage() {
        return `
            <div class="max-w-6xl mx-auto space-y-6">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-900">Integrations</h2>
                    ${Components.createButton('Request Integration', 'primary', 'md', 'fas fa-plus')}
                </div>

                ${Components.createCard('HR Systems', `
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        ${[
                            { name: 'Workday', description: 'Sync employee data and performance metrics', status: 'Connected', icon: 'fas fa-briefcase', color: 'blue' },
                            { name: 'SAP SuccessFactors', description: 'Integrate learning records and goals', status: 'Available', icon: 'fas fa-chart-line', color: 'green' },
                            { name: 'Oracle HCM', description: 'Employee lifecycle management integration', status: 'Available', icon: 'fas fa-database', color: 'red' },
                            { name: 'BambooHR', description: 'Employee information and org chart sync', status: 'Available', icon: 'fas fa-users', color: 'purple' },
                            { name: 'ADP Workforce', description: 'Payroll and benefits integration', status: 'Available', icon: 'fas fa-money-bill-wave', color: 'yellow' },
                            { name: 'Cornerstone OnDemand', description: 'Learning management system sync', status: 'Available', icon: 'fas fa-graduation-cap', color: 'indigo' }
                        ].map(integration => `
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center mb-3">
                                    <div class="w-12 h-12 bg-${integration.color}-100 rounded-lg flex items-center justify-center">
                                        <i class="${integration.icon} text-${integration.color}-600"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="font-semibold text-gray-900">${integration.name}</h4>
                                        ${Components.createBadge(integration.status, integration.status === 'Connected' ? 'success' : 'default')}
                                    </div>
                                </div>

                                <p class="text-sm text-gray-600 mb-4">${integration.description}</p>

                                <div class="space-y-2">
                                    ${integration.status === 'Connected'
                                        ? Components.createButton('Configure', 'primary', 'sm', 'fas fa-cog')
                                        : Components.createButton('Connect', 'outline', 'sm', 'fas fa-plug')
                                    }
                                    ${Components.createButton('Learn More', 'outline', 'sm', 'fas fa-info-circle')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `)}

                ${Components.createCard('Communication Tools', `
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        ${[
                            { name: 'Microsoft Teams', description: 'Schedule sessions directly in Teams', status: 'Connected', icon: 'fab fa-microsoft', color: 'blue' },
                            { name: 'Slack', description: 'Get notifications and updates in Slack', status: 'Available', icon: 'fab fa-slack', color: 'purple' },
                            { name: 'Zoom', description: 'Seamless video conferencing integration', status: 'Connected', icon: 'fas fa-video', color: 'blue' },
                            { name: 'Google Workspace', description: 'Calendar and document integration', status: 'Available', icon: 'fab fa-google', color: 'red' }
                        ].map(integration => `
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <div class="flex items-center mb-3">
                                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                        <i class="${integration.icon} text-${integration.color}-600 text-xl"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="font-semibold text-gray-900">${integration.name}</h4>
                                        ${Components.createBadge(integration.status, integration.status === 'Connected' ? 'success' : 'default')}
                                    </div>
                                </div>

                                <p class="text-sm text-gray-600 mb-4">${integration.description}</p>

                                <div class="space-y-2">
                                    ${integration.status === 'Connected'
                                        ? Components.createButton('Configure', 'primary', 'sm', 'fas fa-cog')
                                        : Components.createButton('Connect', 'outline', 'sm', 'fas fa-plug')
                                    }
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `)}

                ${Components.createCard('API & Webhooks', `
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-900">API Access</h3>
                            ${Components.createButton('Generate API Key', 'outline', 'sm', 'fas fa-key')}
                        </div>

                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="font-medium text-gray-900">API Endpoint</span>
                                ${Components.createButton('Copy', 'outline', 'sm', 'fas fa-copy')}
                            </div>
                            <code class="text-sm text-gray-600">https://api.edutech.com/v1/</code>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Available Endpoints</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• /users - User management</li>
                                    <li>• /sessions - Session data</li>
                                    <li>• /analytics - Usage analytics</li>
                                    <li>• /content - Learning content</li>
                                </ul>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">Webhook Events</h4>
                                <ul class="text-sm text-gray-600 space-y-1">
                                    <li>• user.created</li>
                                    <li>• session.completed</li>
                                    <li>• goal.achieved</li>
                                    <li>• content.viewed</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                `)}
            </div>
        `;
    }

    initializePageInteractivity(pageId) {
        // Page-specific JavaScript initialization
        switch (pageId) {
            case 'analytics':
                this.initializeCharts();
                break;
            case 'booking':
                this.initializeCalendar();
                break;
            default:
                break;
        }
    }

    initializeCharts() {
        // Initialize Chart.js charts for analytics page
        setTimeout(() => {
            const engagementCtx = document.getElementById('engagement-chart');
            if (engagementCtx) {
                new Chart(engagementCtx, {
                    type: 'line',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                        datasets: [{
                            label: 'User Engagement (%)',
                            data: [65, 68, 72, 75, 78, 82, 85, 87, 89, 91, 88, 92],
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100
                            }
                        }
                    }
                });
            }

            const sessionCtx = document.getElementById('session-chart');
            if (sessionCtx) {
                new Chart(sessionCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Individual Sessions', 'Group Sessions', 'E-Learning', 'Workshops'],
                        datasets: [{
                            data: [45, 25, 20, 10],
                            backgroundColor: [
                                'rgb(59, 130, 246)',
                                'rgb(16, 185, 129)',
                                'rgb(245, 158, 11)',
                                'rgb(139, 92, 246)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }
        }, 100);
    }

    initializeCalendar() {
        // Initialize calendar functionality
        console.log('Calendar initialized for booking page');

        // Add click handlers for calendar picker
        document.addEventListener('click', (e) => {
            if (e.target.closest('[id$="-calendar"]')) {
                const calendar = e.target.closest('[id$="-calendar"]');
                const input = document.querySelector(`#${calendar.id.replace('-calendar', '')}`);

                // Simple date selection (in a real app, you'd use a proper date picker library)
                if (e.target.matches('.p-2')) {
                    const day = e.target.textContent;
                    const month = new Date().getMonth() + 1;
                    const year = new Date().getFullYear();
                    input.value = `${month}/${day}/${year}`;
                    calendar.classList.add('hidden');
                }
            }
        });
    }
}

// Global page manager instance
window.pageManager = new PageManager();

// Global functions for button handlers
function handleLogin() {
    alert('Login functionality would be implemented here');
    window.navigation.navigateTo('learner-profile');
}

function handleRegister() {
    alert('Registration functionality would be implemented here');
    window.navigation.navigateTo('learner-profile');
}

function handleCorporateLogin() {
    alert('Corporate login functionality would be implemented here');
    window.navigation.navigateTo('corporate-dashboard');
}

function handleGoogleLogin() {
    alert('Google SSO would be implemented here');
}

function handleLinkedInLogin() {
    alert('LinkedIn SSO would be implemented here');
}

function handleMicrosoftLogin() {
    alert('Microsoft SSO would be implemented here');
}

function handleAzureLogin() {
    alert('Azure AD SSO would be implemented here');
}

function handleOktaLogin() {
    alert('Okta SSO would be implemented here');
}

function handleSAMLLogin() {
    alert('SAML SSO would be implemented here');
}
