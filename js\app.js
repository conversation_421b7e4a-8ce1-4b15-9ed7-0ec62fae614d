// Main application initialization
class App {
    constructor() {
        this.initialized = false;
    }

    async init() {
        if (this.initialized) return;

        try {
            // Load configuration
            await window.appConfig.load();
            
            // Initialize navigation
            window.navigation.render();
            
            // Load initial page
            const initialPage = this.getInitialPage();
            window.navigation.navigateTo(initialPage);
            
            // Initialize global event listeners
            this.initializeGlobalEvents();
            
            this.initialized = true;
            console.log('EduTech app initialized successfully');
        } catch (error) {
            console.error('Failed to initialize app:', error);
        }
    }

    getInitialPage() {
        // Check URL hash for initial page
        const hash = window.location.hash.substring(1);
        if (hash) {
            return hash;
        }
        
        // Default to login page
        return 'login';
    }

    initializeGlobalEvents() {
        // Handle browser back/forward
        window.addEventListener('hashchange', () => {
            const page = window.location.hash.substring(1) || 'login';
            window.navigation.navigateTo(page);
        });

        // Handle escape key for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        // Handle clicks outside dropdowns
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.relative')) {
                this.closeAllDropdowns();
            }
        });
    }

    closeAllModals() {
        document.querySelectorAll('[id$="-modal"]').forEach(modal => {
            modal.classList.add('hidden');
        });
    }

    closeAllDropdowns() {
        document.querySelectorAll('[id$="-options"]').forEach(dropdown => {
            dropdown.classList.add('hidden');
        });
    }
}

// Global utility functions
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${
                type === 'success' ? 'check-circle' :
                type === 'error' ? 'exclamation-circle' :
                type === 'warning' ? 'exclamation-triangle' :
                'info-circle'
            } mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    }).format(new Date(date));
}

function formatTime(time) {
    return new Intl.DateTimeFormat('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    }).format(new Date(`2000-01-01T${time}`));
}

// Sample data generators
function generateSampleUsers(count = 10) {
    const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Emily', 'Robert', 'Lisa', 'James', 'Maria'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
    const companies = ['Tech Corp', 'Innovation Inc', 'Global Solutions', 'Future Systems', 'Digital Dynamics'];
    
    return Array.from({ length: count }, (_, i) => ({
        id: i + 1,
        name: `${firstNames[i % firstNames.length]} ${lastNames[i % lastNames.length]}`,
        email: `user${i + 1}@example.com`,
        company: companies[i % companies.length],
        role: ['Employee', 'Manager', 'Director'][i % 3],
        joinDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
        status: ['Active', 'Inactive'][Math.floor(Math.random() * 2)]
    }));
}

function generateSampleAnalytics() {
    return {
        userEngagement: Array.from({ length: 12 }, (_, i) => ({
            month: new Date(2024, i, 1).toLocaleDateString('en-US', { month: 'short' }),
            value: Math.floor(Math.random() * 100) + 50
        })),
        sessionBookings: Array.from({ length: 7 }, (_, i) => ({
            day: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { weekday: 'short' }),
            value: Math.floor(Math.random() * 20) + 5
        })),
        coachRatings: Array.from({ length: 10 }, (_, i) => ({
            coach: `Coach ${i + 1}`,
            rating: (Math.random() * 2 + 3).toFixed(1)
        }))
    };
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new App();
    app.init();
});

// Make app globally available for debugging
window.app = new App();
