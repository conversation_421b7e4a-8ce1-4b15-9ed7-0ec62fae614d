// Navigation management
class Navigation {
    constructor() {
        this.currentPage = 'login';
        this.navigationContainer = document.getElementById('navigation');
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', () => {
            this.toggleMobileMenu();
        });

        document.getElementById('close-sidebar').addEventListener('click', () => {
            this.closeMobileMenu();
        });

        document.getElementById('mobile-menu-overlay').addEventListener('click', () => {
            this.closeMobileMenu();
        });
    }

    toggleMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-menu-overlay');
        
        sidebar.classList.toggle('-translate-x-full');
        overlay.classList.toggle('hidden');
    }

    closeMobileMenu() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('mobile-menu-overlay');
        
        sidebar.classList.add('-translate-x-full');
        overlay.classList.add('hidden');
    }

    render() {
        const navigationItems = window.appConfig.getNavigationItems();
        
        this.navigationContainer.innerHTML = navigationItems.map(item => {
            if (item.children) {
                return `
                    <div class="mb-4">
                        <div class="flex items-center px-3 py-2 text-sm font-medium text-gray-600 uppercase tracking-wider">
                            <i class="${item.icon} mr-2"></i>
                            ${item.label}
                        </div>
                        <div class="ml-4 space-y-1">
                            ${item.children.map(child => `
                                <a href="#" 
                                   class="nav-link flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200"
                                   data-page="${child.id}">
                                    <i class="${child.icon} mr-3 text-gray-400"></i>
                                    ${child.label}
                                </a>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else {
                return `
                    <div class="mb-2">
                        <a href="#" 
                           class="nav-link flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200"
                           data-page="${item.id}">
                            <i class="${item.icon} mr-3 text-gray-400"></i>
                            ${item.label}
                        </a>
                    </div>
                `;
            }
        }).join('');

        // Add click listeners to navigation links
        this.navigationContainer.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pageId = link.getAttribute('data-page');
                this.navigateTo(pageId);
                this.closeMobileMenu(); // Close mobile menu on navigation
            });
        });

        // Set active state for current page
        this.setActiveNavItem(this.currentPage);
    }

    navigateTo(pageId) {
        this.currentPage = pageId;
        this.setActiveNavItem(pageId);
        
        // Update page title
        const pageTitle = this.getPageTitle(pageId);
        document.getElementById('page-title').textContent = pageTitle;
        
        // Load page content
        window.pageManager.loadPage(pageId);
    }

    setActiveNavItem(pageId) {
        // Remove active state from all nav links
        this.navigationContainer.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('bg-primary-100', 'text-primary-700', 'border-primary-500');
            link.classList.add('text-gray-700');
        });

        // Add active state to current page
        const activeLink = this.navigationContainer.querySelector(`[data-page="${pageId}"]`);
        if (activeLink) {
            activeLink.classList.remove('text-gray-700');
            activeLink.classList.add('bg-primary-100', 'text-primary-700');
        }
    }

    getPageTitle(pageId) {
        const titles = {
            'login': 'Login',
            'register': 'Register',
            'corporate-login': 'Corporate Login',
            'learner-profile': 'Learner Profile',
            'development-needs': 'Development Needs',
            'booking': 'Book Sessions',
            'community': 'Community',
            'e-modules': 'E-Learning Modules',
            'recommended-coaches': 'Recommended Coaches',
            'coach-profile': 'Coach Profile',
            'availability': 'Availability Management',
            'content-upload': 'Content Upload',
            'group-sessions': 'Group Sessions',
            'payout': 'Payout Dashboard',
            'corporate-dashboard': 'Corporate Dashboard',
            'employee-management': 'Employee Management',
            'analytics': 'Analytics',
            'branded-communities': 'Branded Communities',
            'enterprise-billing': 'Enterprise Billing',
            'integrations': 'Integrations',
            'ai-coach-matching': 'AI Coach Matching',
            'ai-recommendations': 'AI Recommendations',
            'payments': 'Payments',
            'settings': 'Settings'
        };
        return titles[pageId] || 'Dashboard';
    }
}

// Global navigation instance
window.navigation = new Navigation();
