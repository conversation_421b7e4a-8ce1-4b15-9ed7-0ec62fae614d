# EduTech - Professional Development Platform

A modern SaaS dashboard prototype built with HTML, TailwindCSS, and Vanilla JavaScript. This is a comprehensive multi-page application that simulates a professional development platform with different user roles and features.

## Features

### 🔐 Authentication
- Login and Register pages
- Corporate Admin login
- Social SSO buttons (Google, LinkedIn, Microsoft)
- Role-based access control

### 👨‍🎓 Learner/Employee Features
- **Profile Management**: Complete profile with education, goals, and preferences
- **Development Needs**: Multi-select skill areas and AI recommendations
- **Session Booking**: Calendar integration, 1:1 and group sessions
- **Community**: Growth circles, discussions, and member networking
- **E-Learning**: Video modules with progress tracking
- **Coach Recommendations**: AI-powered coach matching

### 👨‍🏫 Coach/Mentor Features
- **Coach Profile**: Expertise, certifications, and intro videos
- **Availability Management**: Calendar sync and time slot management
- **Content Upload**: Videos, resources, and social media integration
- **Group Sessions**: Create and manage workshops and group programs
- **Payout Dashboard**: Earnings tracking and payment management

### 🏢 Corporate Admin Features
- **Dashboard**: Overview of employee engagement and metrics
- **Employee Management**: Invite and manage employee accounts
- **Analytics**: Detailed engagement metrics with Chart.js visualizations
- **Branded Communities**: Create company-specific learning circles
- **Enterprise Billing**: Subscription management and invoicing
- **Integrations**: HR systems (Workday, SAP, Oracle) and communication tools

### 🤖 AI Features
- **AI Coach Matching**: Intelligent coach recommendations based on preferences
- **AI Recommendations**: Personalized learning paths and content suggestions
- **Learning Analytics**: AI-powered insights and patterns

### ⚙️ System Features
- **Payments**: Flexible pricing tiers (Free, Pay-as-you-Go, Subscription)
- **Settings**: Profile, notifications, and localization options
- **Responsive Design**: Fully responsive for desktop and mobile
- **Dynamic Navigation**: Config-driven page loading

## Configuration

The application uses a `config.json` file to enable/disable features:

```json
{
  "learner": true,
  "coach": true,
  "corporateAdmin": true,
  "aiFeatures": true,
  "payments": true
}
```

## File Structure

```
├── index.html              # Main application file
├── config.json            # Feature configuration
├── css/
│   └── custom.css         # Custom styles and animations
├── js/
│   ├── app.js            # Main application initialization
│   ├── config.js         # Configuration management
│   ├── navigation.js     # Navigation and routing
│   ├── pages.js          # Page templates and content
│   └── components.js     # Reusable UI components
└── README.md
```

## Getting Started

1. **Clone or download** the project files
2. **Open `index.html`** in a modern web browser
3. **Navigate** through different pages using the sidebar menu
4. **Customize** features by editing `config.json`

## Technology Stack

- **HTML5**: Semantic markup and structure
- **TailwindCSS**: Utility-first CSS framework via CDN
- **Vanilla JavaScript**: No frameworks, pure ES6+ JavaScript
- **Chart.js**: Data visualization for analytics
- **Font Awesome**: Icons and visual elements

## Key Components

### Navigation System
- Responsive sidebar for desktop
- Hamburger menu for mobile
- Dynamic menu generation based on config
- Hash-based routing

### Component Library
- Reusable UI components (buttons, cards, modals, tabs)
- Dropdown menus and multi-selects
- Calendar pickers and progress bars
- Badges and avatars

### Page Management
- Dynamic page loading
- Template-based content generation
- Sample data integration
- Interactive elements

## Sample Data

The application includes realistic sample data for:
- User profiles and employee lists
- Coach information and ratings
- Session bookings and schedules
- Community discussions and analytics
- Learning modules and progress

## Responsive Design

- **Desktop**: Full sidebar navigation with detailed layouts
- **Mobile**: Hamburger menu with optimized mobile layouts
- **Tablet**: Adaptive grid systems and touch-friendly interfaces

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Customization

### Adding New Pages
1. Add page configuration in `js/config.js`
2. Create page template in `js/pages.js`
3. Update navigation items

### Modifying Styles
- Edit `css/custom.css` for custom styles
- Use TailwindCSS utility classes for rapid styling
- Customize color scheme in the Tailwind config

### Extending Functionality
- Add new components in `js/components.js`
- Implement real API calls in place of sample data
- Add authentication logic and session management

## Production Considerations

This is a prototype/demo application. For production use, consider:

- **Backend Integration**: Replace sample data with real API calls
- **Authentication**: Implement proper user authentication and session management
- **State Management**: Add proper state management for complex interactions
- **Performance**: Optimize for production with bundling and minification
- **Security**: Implement proper security measures and data validation
- **Testing**: Add comprehensive testing suite
- **Accessibility**: Enhance accessibility features and ARIA labels

## License

This project is provided as-is for demonstration purposes. Feel free to use and modify as needed.

## Support

This is a prototype application. For questions or customizations, refer to the code comments and documentation within the JavaScript files.
