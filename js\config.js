// Configuration management
class Config {
    constructor() {
        this.config = null;
        this.currentRole = 'learner'; // Default role
    }

    async load() {
        try {
            const response = await fetch('./config.json');
            this.config = await response.json();
            return this.config;
        } catch (error) {
            console.error('Failed to load config:', error);
            // Fallback config
            this.config = {
                learner: true,
                coach: true,
                corporateAdmin: true,
                aiFeatures: true,
                payments: true
            };
            return this.config;
        }
    }

    isEnabled(feature) {
        return this.config && this.config[feature] === true;
    }

    setRole(role) {
        this.currentRole = role;
        localStorage.setItem('currentRole', role);
    }

    getRole() {
        return localStorage.getItem('currentRole') || this.currentRole;
    }

    getNavigationItems() {
        const items = [];

        // Authentication pages (always available)
        items.push({
            id: 'auth',
            label: 'Authentication',
            icon: 'fas fa-sign-in-alt',
            children: [
                { id: 'login', label: 'Login', icon: 'fas fa-sign-in-alt' },
                { id: 'register', label: 'Register', icon: 'fas fa-user-plus' },
                { id: 'corporate-login', label: 'Corporate Login', icon: 'fas fa-building' }
            ]
        });

        // Learner/Employee pages
        if (this.isEnabled('learner')) {
            items.push({
                id: 'learner',
                label: 'Learner Dashboard',
                icon: 'fas fa-user-graduate',
                children: [
                    { id: 'learner-profile', label: 'Profile', icon: 'fas fa-user' },
                    { id: 'development-needs', label: 'Development Needs', icon: 'fas fa-target' },
                    { id: 'booking', label: 'Book Sessions', icon: 'fas fa-calendar-plus' },
                    { id: 'community', label: 'Community', icon: 'fas fa-users' },
                    { id: 'e-modules', label: 'E-Learning', icon: 'fas fa-play-circle' },
                    { id: 'recommended-coaches', label: 'Recommended Coaches', icon: 'fas fa-star' }
                ]
            });
        }

        // Coach/Mentor pages
        if (this.isEnabled('coach')) {
            items.push({
                id: 'coach',
                label: 'Coach Dashboard',
                icon: 'fas fa-chalkboard-teacher',
                children: [
                    { id: 'coach-profile', label: 'Coach Profile', icon: 'fas fa-id-card' },
                    { id: 'availability', label: 'Availability', icon: 'fas fa-calendar-check' },
                    { id: 'content-upload', label: 'Content Upload', icon: 'fas fa-upload' },
                    { id: 'group-sessions', label: 'Group Sessions', icon: 'fas fa-users-cog' },
                    { id: 'payout', label: 'Payouts', icon: 'fas fa-money-bill-wave' }
                ]
            });
        }

        // Corporate Admin pages
        if (this.isEnabled('corporateAdmin')) {
            items.push({
                id: 'corporate',
                label: 'Corporate Admin',
                icon: 'fas fa-building',
                children: [
                    { id: 'corporate-dashboard', label: 'Dashboard', icon: 'fas fa-chart-line' },
                    { id: 'employee-management', label: 'Employee Management', icon: 'fas fa-users-cog' },
                    { id: 'analytics', label: 'Analytics', icon: 'fas fa-chart-bar' },
                    { id: 'branded-communities', label: 'Branded Communities', icon: 'fas fa-sitemap' },
                    { id: 'enterprise-billing', label: 'Billing', icon: 'fas fa-file-invoice-dollar' },
                    { id: 'integrations', label: 'Integrations', icon: 'fas fa-plug' }
                ]
            });
        }

        // AI Features
        if (this.isEnabled('aiFeatures')) {
            items.push({
                id: 'ai',
                label: 'AI Features',
                icon: 'fas fa-robot',
                children: [
                    { id: 'ai-coach-matching', label: 'AI Coach Matching', icon: 'fas fa-magic' },
                    { id: 'ai-recommendations', label: 'AI Recommendations', icon: 'fas fa-lightbulb' }
                ]
            });
        }

        // System pages
        items.push({
            id: 'system',
            label: 'System',
            icon: 'fas fa-cog',
            children: [
                { id: 'settings', label: 'Settings', icon: 'fas fa-cog' }
            ]
        });

        // Payments
        if (this.isEnabled('payments')) {
            items.push({
                id: 'payments',
                label: 'Payments',
                icon: 'fas fa-credit-card'
            });
        }

        return items;
    }
}

// Global config instance
window.appConfig = new Config();
